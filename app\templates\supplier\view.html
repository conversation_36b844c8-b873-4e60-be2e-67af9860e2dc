{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="card-title mb-0">{{ title }}</h3>
                            <small class="text-muted">查看供应商的详细信息和相关数据</small>
                        </div>
                        <div class="card-tools">
                            <a href="{{ url_for('supplier.edit', id=supplier.id) }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit"></i> 编辑
                            </a>
                            <a href="{{ url_for('supplier.index') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left"></i> 返回
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">供应商名称</th>
                                    <td>{{ supplier.name }}</td>
                                </tr>
                                <tr>
                                    <th>分类</th>
                                    <td>{{ supplier.category.name if supplier.category else '-' }}</td>
                                </tr>
                                <tr>
                                    <th>联系人</th>
                                    <td>{{ supplier.contact_person }}</td>
                                </tr>
                                <tr>
                                    <th>联系电话</th>
                                    <td>{{ supplier.phone }}</td>
                                </tr>
                                <tr>
                                    <th>电子邮箱</th>
                                    <td>{{ supplier.email or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>地址</th>
                                    <td>{{ supplier.address }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">营业执照号</th>
                                    <td>{{ supplier.business_license }}</td>
                                </tr>
                                <tr>
                                    <th>税务登记号</th>
                                    <td>{{ supplier.tax_id or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>开户银行</th>
                                    <td>{{ supplier.bank_name or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>银行账号</th>
                                    <td>{{ supplier.bank_account or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>评级</th>
                                    <td>
                                        {% if supplier.rating %}
                                        <div class="text-warning">
                                            {% for i in range(supplier.rating|int) %}
                                            <i class="fas fa-star"></i>
                                            {% endfor %}
                                            {% if supplier.rating % 1 > 0 %}
                                            <i class="fas fa-star-half-alt"></i>
                                            {% endif %}
                                            ({{ supplier.rating }})
                                        </div>
                                        {% else %}
                                        <span class="text-muted">暂无评级</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>
                                        {% if supplier.status == 1 %}
                                        <span class="badge badge-success">合作中</span>
                                        {% else %}
                                        <span class="badge badge-secondary">已停用</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 供应商证书 -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="card-title mb-0">证书信息</h3>
                            <small class="text-muted">管理供应商的资质证书和认证文件</small>
                        </div>
                        <div class="card-tools">
                            <a href="{{ url_for('supplier_certificate.create') }}?supplier_id={{ supplier.id }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> 添加证书
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>证书类型</th>
                                    <th>证书编号</th>
                                    <th>发证机构</th>
                                    <th>发证日期</th>
                                    <th>过期日期</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for cert in supplier.certificates %}
                                <tr>
                                    <td>{{ cert.certificate_type }}</td>
                                    <td>{{ cert.certificate_number }}</td>
                                    <td>{{ cert.issuing_authority }}</td>
                                    <td>{{  cert.issue_date|format_datetime('%Y-%m-%d')  }}</td>
                                    <td>{{  cert.expiry_date|format_datetime('%Y-%m-%d')  }}</td>
                                    <td>
                                        {% if cert.status == '有效' %}
                                        <span class="badge badge-success">{{ cert.status }}</span>
                                        {% elif cert.status == '即将过期' %}
                                        <span class="badge badge-warning">{{ cert.status }}</span>
                                        {% else %}
                                        <span class="badge badge-danger">{{ cert.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('supplier_certificate.view', id=cert.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">暂无证书信息</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 供应商产品 -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="card-title mb-0">产品信息</h3>
                            <small class="text-muted">查看和管理供应商提供的产品列表</small>
                        </div>
                        <div class="card-tools">
                            <a href="{{ url_for('supplier_product.create') }}?supplier_id={{ supplier.id }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> 添加产品
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>产品名称</th>
                                    <th>食材</th>
                                    <th>型号</th>
                                    <th>规格</th>
                                    <th>单价</th>
                                    <th>审核状态</th>
                                    <th>上架状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in supplier.products %}
                                <tr>
                                    <td>{{ product.product_name or product.ingredient.name }}</td>
                                    <td>{{ product.ingredient.name }}</td>
                                    <td>{{ product.model_number or '-' }}</td>
                                    <td>{{ product.specification or '-' }}</td>
                                    <td>¥{{ product.price }}</td>
                                    <td>
                                        {% if product.shelf_status == 0 %}
                                        <span class="badge badge-warning">待审核</span>
                                        {% elif product.shelf_status == 1 %}
                                        <span class="badge badge-success">已审核</span>
                                        {% else %}
                                        <span class="badge badge-danger">已拒绝</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if product.is_available == 1 %}
                                        <span class="badge badge-success">已上架</span>
                                        {% else %}
                                        <span class="badge badge-secondary">未上架</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('supplier_product.view', id=product.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center">暂无产品信息</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 合作学校 -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="card-title mb-0">合作学校</h3>
                            <small class="text-muted">管理供应商与学校的合作关系和合同信息</small>
                        </div>
                        <div class="card-tools">
                            <a href="{{ url_for('supplier_school.create') }}?supplier_id={{ supplier.id }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> 添加合作学校
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>学校名称</th>
                                    <th>合同编号</th>
                                    <th>合作开始日期</th>
                                    <th>合作结束日期</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for relation in supplier.school_relations %}
                                <tr>
                                    <td>{{ relation.area.name }}</td>
                                    <td>{{ relation.contract_number or '-' }}</td>
                                    <td>{{  relation.start_date|format_datetime('%Y-%m-%d')  }}</td>
                                    <td>{{  relation.end_date|format_datetime('%Y-%m-%d') if relation.end_date else '长期'  }}</td>
                                    <td>
                                        {% if relation.status == 1 %}
                                        <span class="badge badge-success">有效</span>
                                        {% else %}
                                        <span class="badge badge-secondary">已终止</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('supplier_school.edit', id=relation.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="6" class="text-center">暂无合作学校</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
