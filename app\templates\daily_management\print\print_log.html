{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/print-styles.css') }}">
<style nonce="{{ csp_nonce }}">
    body {
        font-size: 14pt;
    }

    .section-title {
        font-size: 16pt;
        font-weight: bold;
        margin-top: 20px;
        margin-bottom: 10px;
        border-bottom: 1px solid #4e73df;
        padding-bottom: 5px;
        color: #4e73df;
    }

    .info-row {
        margin-bottom: 10px;
    }

    .info-label {
        font-weight: bold;
    }

    .print-page {
        padding: 20mm;
        margin-bottom: 20px;
        border: 1px solid #ddd;
        background: #fff;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        body {
            font-size: 12pt;
        }

        .section-title {
            font-size: 14pt;
        }

        .print-page {
            padding: 0;
            margin: 0;
            border: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 打印控制按钮 -->
    <div class="no-print mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">打印预览</h6>
                <div>
                    <a href="{{ url_for('daily_management.logs') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left mr-1"></i> 返回日志列表
                    </a>
                    <button class="print-button" class="btn btn-primary btn-sm">
                        <i class="fas fa-print mr-1"></i> 打印
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle mr-1"></i> 打印预览模式。点击"打印"按钮开始打印，或使用浏览器的打印功能（Ctrl+P）。
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <a href="{{ url_for('daily_management.print_inspections', log_id=log.id) }}" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-tasks mr-1"></i> 仅打印检查记录
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ url_for('daily_management.print_companion_by_id', log_id=log.id) }}" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-utensils mr-1"></i> 仅打印陪餐记录
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ url_for('daily_management.print_training_by_id', log_id=log.id) }}" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-chalkboard-teacher mr-1"></i> 仅打印培训记录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 打印内容 -->
    <div class="print-preview">
        <!-- 第一页：日志基本信息 -->
        <div class="print-page avoid-break">
            <div class="print-page-indicator no-print">第1页</div>

            <!-- 页眉 -->
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 食堂日常管理日志</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>

            <!-- 基本信息 -->
            <div class="section-title">基本信息</div>
            <div class="row info-row">
                <div class="col-md-4">
                    <span class="info-label">管理员：</span> {{ log.manager or '未填写' }}
                </div>
                <div class="col-md-4">
                    <span class="info-label">天气：</span> {{ log.weather or '未填写' }}
                </div>
                <div class="col-md-4">
                    <span class="info-label">创建时间：</span> {{ log.created_at|safe_datetime('%Y-%m-%d %H:%M') }}
                </div>
            </div>

            <!-- 就餐人数 -->
            <div class="section-title">就餐人数</div>
            <div class="row info-row">
                <div class="col-md-3">
                    <span class="info-label">学生：</span> {{ log.student_count or 0 }} 人
                </div>
                <div class="col-md-3">
                    <span class="info-label">教师：</span> {{ log.teacher_count or 0 }} 人
                </div>
                <div class="col-md-3">
                    <span class="info-label">其他：</span> {{ log.other_count or 0 }} 人
                </div>
                <div class="col-md-3">
                    <span class="info-label">总计：</span> {{ (log.student_count or 0) + (log.teacher_count or 0) + (log.other_count or 0) }} 人
                </div>
            </div>

            <!-- 菜单信息 -->
            <div class="section-title">菜单信息</div>
            <div class="row info-row">
                <div class="col-md-4">
                    <div class="info-label">早餐菜单：</div>
                    <p>{{ log.breakfast_menu or '未填写' }}</p>
                </div>
                <div class="col-md-4">
                    <div class="info-label">午餐菜单：</div>
                    <p>{{ log.lunch_menu or '未填写' }}</p>
                </div>
                <div class="col-md-4">
                    <div class="info-label">晚餐菜单：</div>
                    <p>{{ log.dinner_menu or '未填写' }}</p>
                </div>
            </div>

            <!-- 食物浪费 -->
            <div class="section-title">食物浪费</div>
            <div class="row info-row">
                <div class="col-md-12">
                    <span class="info-label">食物浪费量：</span> {{ log.food_waste or 0 }} 千克
                </div>
            </div>

            <!-- 运营总结 -->
            <div class="section-title">运营总结</div>
            <div class="row info-row">
                <div class="col-md-12">
                    <p>{{ log.operation_summary or '未填写' }}</p>
                </div>
            </div>
        </div>

        <!-- 第二页：检查记录 -->
        {% if morning_inspections or noon_inspections or evening_inspections %}
        <div class="print-page avoid-break page-break">
            <div class="print-page-indicator no-print">第2页</div>

            <div class="text-center mb-4">
                <h1>{{ school.name }} - 检查记录</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>

            <!-- 早餐检查记录 -->
            {% if morning_inspections %}
            <div class="section-title">早餐检查记录</div>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>检查项目</th>
                        <th>状态</th>
                        <th>描述</th>
                        <th>检查时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for inspection in morning_inspections %}
                    <tr>
                        <td>{{ inspection.inspection_item }}</td>
                        <td>{{ '正常' if inspection.status == 'normal' else '异常' }}</td>
                        <td>{{ inspection.description or '无' }}</td>
                        <td>{{ inspection.inspection_time|safe_datetime('%H:%M') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}

            <!-- 午餐检查记录 -->
            {% if noon_inspections %}
            <div class="section-title">午餐检查记录</div>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>检查项目</th>
                        <th>状态</th>
                        <th>描述</th>
                        <th>检查时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for inspection in noon_inspections %}
                    <tr>
                        <td>{{ inspection.inspection_item }}</td>
                        <td>{{ '正常' if inspection.status == 'normal' else '异常' }}</td>
                        <td>{{ inspection.description or '无' }}</td>
                        <td>{{ inspection.inspection_time|safe_datetime('%H:%M') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}

            <!-- 晚餐检查记录 -->
            {% if evening_inspections %}
            <div class="section-title">晚餐检查记录</div>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>检查项目</th>
                        <th>状态</th>
                        <th>描述</th>
                        <th>检查时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for inspection in evening_inspections %}
                    <tr>
                        <td>{{ inspection.inspection_item }}</td>
                        <td>{{ '正常' if inspection.status == 'normal' else '异常' }}</td>
                        <td>{{ inspection.description or '无' }}</td>
                        <td>{{ inspection.inspection_time|safe_datetime('%H:%M') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}

        <!-- 第三页：陪餐记录 -->
        {% if companions %}
        <div class="print-page avoid-break page-break">
            <div class="print-page-indicator no-print">第3页</div>

            <div class="text-center mb-4">
                <h1>{{ school.name }} - 陪餐记录</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>

            <div class="section-title">陪餐记录列表</div>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>陪餐人</th>
                        <th>角色</th>
                        <th>餐次</th>
                        <th>陪餐时间</th>
                        <th>口味评分</th>
                        <th>卫生评分</th>
                        <th>服务评分</th>
                    </tr>
                </thead>
                <tbody>
                    {% for companion in companions %}
                    <tr>
                        <td>{{ companion.companion_name }}</td>
                        <td>{{ companion.companion_role }}</td>
                        <td>{{ companion.meal_type }}</td>
                        <td>{{ companion.dining_time|safe_datetime('%H:%M') }}</td>
                        <td>{{ companion.taste_rating or 0 }}</td>
                        <td>{{ companion.hygiene_rating or 0 }}</td>
                        <td>{{ companion.service_rating or 0 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <div class="section-title">陪餐评价详情</div>
            {% for companion in companions %}
            <div class="card mb-3">
                <div class="card-header">
                    <strong>{{ companion.companion_name }}</strong> - {{ companion.meal_type }} ({{ companion.dining_time|safe_datetime('%H:%M') }})
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-label">评价意见：</div>
                            <p>{{ companion.comments or '无' }}</p>
                        </div>
                        <div class="col-md-6">
                            <div class="info-label">改进建议：</div>
                            <p>{{ companion.suggestions or '无' }}</p>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- 签名区域 -->
        <div class="print-page avoid-break page-break">
            <div class="print-page-indicator no-print">签名页</div>

            <div class="text-center mb-4">
                <h1>{{ school.name }} - 食堂日常管理日志</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>

            <div class="section-title">签名确认</div>
            <div class="row mt-5">
                <div class="col-md-4">
                    <div class="text-center">
                        <p>食堂管理员签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <p>学校负责人签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <p>监督人签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
            </div>

            <div class="text-center mt-5">
                <p>（本文档由系统自动生成，打印后有效）</p>
            </div>
        </div>
    </div>
</div>

<!-- 打印控制按钮（固定在右下角） -->
<div class="print-controls no-print">
    <button class="print-button"><i class="fas fa-print mr-1"></i> 打印</button>
    <button data-action="safe-navigate" data-navigate-code="window.location.href=" style="cursor: pointer;"{{ url_for('daily_management.logs') }}'"><i class="fas fa-times mr-1"></i> 取消</button>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 打印预览初始化
        $('.print-page').each(function(index) {
            $(this).find('.print-page-indicator').text('第' + (index + 1) + '页');
        });
    });
</script>
{% endblock %}
