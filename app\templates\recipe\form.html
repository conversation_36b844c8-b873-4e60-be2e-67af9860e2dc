{% extends 'base.html' %}

{% block title %}
{% if recipe %}编辑食谱{% else %}添加食谱{% endif %}
{% endblock %}

{% block styles %}
{{ super() }}
<link href="{{ url_for('static', filename='vendor/select2/css/select2.min.css') }}" rel="stylesheet" />
<link href="{{ url_for('static', filename='vendor/select2/css/select2-bootstrap4.min.css') }}" rel="stylesheet" />
<style nonce="{{ csp_nonce }}">
    .ingredient-item, .process-item {
        position: relative;
        padding: 15px;
        margin-bottom: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background-color: #f9f9f9;
    }
    .remove-btn {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    .process-ingredients {
        margin-top: 10px;
        padding: 10px;
        background-color: #f0f0f0;
        border-radius: 5px;
    }
    .process-ingredient-item {
        padding: 10px;
        margin-bottom: 10px;
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        background-color: #fff;
    }
    .drag-handle {
        cursor: move;
        color: #999;
    }
    .preview-image {
        max-height: 200px;
        max-width: 100%;
        margin-top: 10px;
    }

    /* 表单验证样式 */
    .is-invalid {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }
    .select2-container--bootstrap4 .select2-selection.is-invalid {
        border-color: #dc3545 !important;
    }
    .form-group label.required:after {
        content: " *";
        color: #dc3545;
    }

    /* 加载状态样式 */
    .btn-loading {
        position: relative;
        pointer-events: none;
    }
    .btn-loading:after {
        content: "";
        position: absolute;
        width: 1rem;
        height: 1rem;
        top: calc(50% - 0.5rem);
        left: calc(50% - 0.5rem);
        border: 2px solid rgba(255, 255, 255, 0.5);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 0.8s linear infinite;
    }
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    
    /* 用户自定义食谱样式 */
    .user-defined-box {
        border: 2px solid #ffc107; 
        padding: 15px; 
        border-radius: 5px; 
        background-color: #fff8e1;
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% if recipe %}编辑食谱{% else %}添加食谱{% endif %}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('recipe.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" id="recipeForm">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <!-- 基本信息 -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h4 class="mb-0">基本信息</h4>
                            </div>
                            <div class="card-body">
                                <!-- 用户自定义食谱选项 -->
                                <div class="user-defined-box">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="is_user_defined" name="is_user_defined" value="1" {% if recipe and recipe.is_user_defined %}checked{% endif %}>
                                        <label class="form-check-label" for="is_user_defined"><strong>标记为用户自定义食谱</strong></label>
                                    </div>
                                    <small class="form-text text-muted">用户自定义食谱将在菜单规划中优先显示</small>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="name" class="required">食谱名称</label>
                                            <input type="text" class="form-control" id="name" name="name" value="{{ recipe.name if recipe else '' }}" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="category_id">食谱分类</label>
                                            <select class="form-control" id="category_id" name="category_id">
                                                <option value="">-- 请选择分类 --</option>
                                                {% for category in categories %}
                                                <option value="{{ category.id }}" {% if recipe and recipe.category_id == category.id %}selected{% endif %}>
                                                    {{ category.name }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="meal_type">适用餐次</label>
                                            <select class="form-control" id="meal_type" name="meal_type">
                                                <option value="">-- 请选择餐次 --</option>
                                                <option value="早餐" {% if recipe and recipe.meal_type == '早餐' %}selected{% endif %}>早餐</option>
                                                <option value="午餐" {% if recipe and recipe.meal_type == '午餐' %}selected{% endif %}>午餐</option>
                                                <option value="晚餐" {% if recipe and recipe.meal_type == '晚餐' %}selected{% endif %}>晚餐</option>
                                                <option value="早餐,午餐" {% if recipe and recipe.meal_type == '早餐,午餐' %}selected{% endif %}>早餐,午餐</option>
                                                <option value="午餐,晚餐" {% if recipe and recipe.meal_type == '午餐,晚餐' %}selected{% endif %}>午餐,晚餐</option>
                                                <option value="早餐,午餐,晚餐" {% if recipe and recipe.meal_type == '早餐,午餐,晚餐' %}selected{% endif %}>早餐,午餐,晚餐</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="cooking_method">烹饪方法</label>
                                            <input type="text" class="form-control" id="cooking_method" name="cooking_method" value="{{ recipe.cooking_method if recipe else '' }}">
                                        </div>
                                        <div class="form-group">
                                            <label for="cooking_time">烹饪时间（分钟）</label>
                                            <input type="number" class="form-control" id="cooking_time" name="cooking_time" value="{{ recipe.cooking_time if recipe else '' }}" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="main_image">食谱图片</label>
                                            <div class="input-group">
                                                <div class="custom-file">
                                                    <input type="file" class="custom-file-input" id="main_image" name="main_image" accept="image/*">
                                                    <label class="custom-file-label" for="main_image">选择文件</label>
                                                </div>
                                            </div>
                                            {% if recipe and recipe.main_image %}
                                            <div class="mt-2">
                                                <img src="{{ url_for('static', filename=recipe.main_image) }}" alt="{{ recipe.name }}" class="preview-image">
                                            </div>
                                            {% else %}
                                            <div class="mt-2" id="imagePreview" style="display: none;">
                                                <img src="" alt="预览" class="preview-image">
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="form-group">
                                            <label for="calories">热量（卡路里）</label>
                                            <input type="number" class="form-control" id="calories" name="calories" value="{{ recipe.calories if recipe else '' }}" min="0">
                                        </div>
                                        <div class="form-group">
                                            <label for="serving_size">标准份量</label>
                                            <input type="number" class="form-control" id="serving_size" name="serving_size" value="{{ recipe.serving_size if recipe else '' }}" min="1">
                                        </div>
                                        <div class="form-group">
                                            <label for="status">状态</label>
                                            <select class="form-control" id="status" name="status">
                                                <option value="1" {% if not recipe or recipe.status == 1 %}selected{% endif %}>启用</option>
                                                <option value="0" {% if recipe and recipe.status == 0 %}selected{% endif %}>停用</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="description">食谱描述</label>
                                            <textarea class="form-control" id="description" name="description" rows="3">{{ recipe.description if recipe else '' }}</textarea>
                                        </div>
                                        <div class="form-group">
                                            <label for="nutrition_info">营养信息</label>
                                            <textarea class="form-control" id="nutrition_info" name="nutrition_info" rows="3">{{ recipe.nutrition_info if recipe else '' }}</textarea>
                                            <small class="form-text text-muted">可以填写食谱的营养成分，如蛋白质、脂肪、碳水化合物等含量</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 注意：食材配比和制作工序部分已被移除 -->

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">保存</button>
                            <a href="{{ url_for('recipe.index') }}" class="btn btn-secondary">取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/select2/select2.min.js') }}"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/bs-custom-file-input/bs-custom-file-input.min.js') }}"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/sortablejs/Sortable.min.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 检查登录状态
        checkAndUpdateLoginStatus().then(function(isLoggedIn) {
            if (!isLoggedIn) {
                toastr.warning('请先登录后再编辑食谱');
                setTimeout(function() {
                    window.location.href = '{{ url_for("auth.login") }}?next={{ request.path }}';
                }, 1500);
                return;
            }
        });

        // 初始化文件上传显示文件名
        bsCustomFileInput.init();

        // 图片预览
        $('#main_image').change(function() {
            if (this.files && this.files[0]) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    $('#imagePreview img').attr('src', e.target.result);
                    $('#imagePreview').show();
                }
                reader.readAsDataURL(this.files[0]);
            } else {
                $('#imagePreview').hide();
            }
        });

        // 表单提交前验证
        $('#recipeForm').submit(function(e) {
            e.preventDefault(); // 阻止默认提交

            // 检查登录状态
            if (!getLoginStatus()) {
                toastr.warning('请先登录后再保存食谱');
                setTimeout(function() {
                    window.location.href = '{{ url_for("auth.login") }}?next={{ request.path }}';
                }, 1500);
                return false;
            }

            // 检查必填字段
            var isValid = true;
            $('#recipeForm [required]').each(function() {
                if (!$(this).val()) {
                    isValid = false;
                    $(this).addClass('is-invalid');

                    // 滚动到第一个错误字段
                    if (isValid === false) {
                        $('html, body').animate({
                            scrollTop: $(this).offset().top - 100
                        }, 200);
                    }
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            if (!isValid) {
                toastr.error('请填写所有必填字段');
                return false;
            }

            // 显示加载状态
            var submitBtn = $('#recipeForm button[type="submit"]');
            var originalText = submitBtn.html();
            submitBtn.html('<i class="fas fa-spinner fa-spin"></i> 保存中...').prop('disabled', true);

            // 使用FormData提交表单（支持文件上传）
            var formData = new FormData(this);

            $.ajax({
                url: $(this).attr('action') || window.location.href,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                },
                success: function(response) {
                    toastr.success('食谱保存成功！');
                    setTimeout(function() {
                        window.location.href = '{{ url_for("recipe.view", id=recipe.id if recipe else 0) }}'.replace('0', response.id || '{{ recipe.id if recipe else 0 }}');
                    }, 1000);
                },
                error: function(xhr, status, error) {
                    // 恢复按钮状态
                    submitBtn.html(originalText).prop('disabled', false);

                    // 处理认证错误
                    if (!handleAuthError(xhr, '{{ request.path }}')) {
                        if (xhr.status === 403 || (xhr.responseText && xhr.responseText.indexOf('CSRF') !== -1)) {
                            toastr.error('CSRF验证失败，请刷新页面后重试');
                        } else {
                            toastr.error(xhr.responseJSON?.message || '保存失败，请稍后重试！');
                        }
                    }
                }
            });

            return false;
        });

        // 输入时移除错误状态
        $('#recipeForm [required]').on('input change', function() {
            if ($(this).val()) {
                $(this).removeClass('is-invalid');
            }
        });
    });
</script>
{% endblock %}
