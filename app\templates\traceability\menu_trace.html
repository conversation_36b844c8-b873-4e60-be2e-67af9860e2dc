{% extends 'base.html' %}

{% block title %}菜单溯源详情{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">菜单溯源详情</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('traceability.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回溯源查询
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 菜单基本信息 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary">
                                    <h4 class="card-title text-white">菜单基本信息</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th class="w-30">菜单ID</th>
                                            <td>{{ menu_plan.id }}</td>
                                        </tr>
                                        <tr>
                                            <th>区域</th>
                                            <td>{{ menu_plan.area.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>计划日期</th>
                                            <td>{{ menu_plan.plan_date }}</td>
                                        </tr>
                                        <tr>
                                            <th>餐次</th>
                                            <td>{{ menu_plan.meal_type }}</td>
                                        </tr>
                                        <tr>
                                            <th>预计就餐人数</th>
                                            <td>{{ menu_plan.expected_diners or '未设置' }}</td>
                                        </tr>
                                        <tr>
                                            <th>实际就餐人数</th>
                                            <td>{{ menu_plan.actual_diners or '未记录' }}</td>
                                        </tr>
                                        <tr>
                                            <th>状态</th>
                                            <td>{{ menu_plan.status }}</td>
                                        </tr>
                                        <tr>
                                            <th>创建人</th>
                                            <td>{{ menu_plan.creator.real_name or menu_plan.creator.username }}</td>
                                        </tr>
                                        <tr>
                                            <th>创建时间</th>
                                            <td>{{ menu_plan.created_at }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success">
                                    <h4 class="card-title text-white">消耗计划信息</h4>
                                </div>
                                <div class="card-body">
                                    {% if consumption_plan %}
                                    <table class="table table-bordered">
                                        <tr>
                                            <th class="w-30">消耗计划ID</th>
                                            <td>{{ consumption_plan.id }}</td>
                                        </tr>
                                        <tr>
                                            <th>状态</th>
                                            <td>{{ consumption_plan.status }}</td>
                                        </tr>
                                        <tr>
                                            <th>创建人</th>
                                            <td>{{ consumption_plan.creator.real_name or consumption_plan.creator.username }}</td>
                                        </tr>
                                        <tr>
                                            <th>审核人</th>
                                            <td>{{ consumption_plan.approver.real_name or consumption_plan.approver.username if consumption_plan.approver else '未审核' }}</td>
                                        </tr>
                                        <tr>
                                            <th>创建时间</th>
                                            <td>{{ consumption_plan.created_at }}</td>
                                        </tr>
                                        <tr>
                                            <th>更新时间</th>
                                            <td>{{ consumption_plan.updated_at }}</td>
                                        </tr>
                                        <tr>
                                            <th>备注</th>
                                            <td>{{ consumption_plan.notes or '无' }}</td>
                                        </tr>
                                    </table>
                                    {% else %}
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i> 该菜单计划尚未创建消耗计划
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 菜单食谱列表 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-info">
                                    <h4 class="card-title text-white">菜单食谱列表</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>食谱名称</th>
                                                    <th>分类</th>
                                                    <th>计划数量</th>
                                                    <th>实际数量</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for menu_recipe in menu_recipes %}
                                                <tr>
                                                    <td>{{ menu_recipe.recipe.name }}</td>
                                                    <td>{{ menu_recipe.recipe.category_rel.name if menu_recipe.recipe.category_rel else menu_recipe.recipe.category }}</td>
                                                    <td>{{ menu_recipe.planned_quantity }}</td>
                                                    <td>{{ menu_recipe.actual_quantity or '未记录' }}</td>
                                                    <td>
                                                        <a href="{{ url_for('recipe.view', id=menu_recipe.recipe_id) }}" class="btn btn-info btn-sm">
                                                            <i class="fas fa-eye"></i> 查看食谱
                                                        </a>
                                                    </td>
                                                </tr>
                                                {% else %}
                                                <tr>
                                                    <td colspan="5" class="text-center">暂无食谱数据</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 批次信息 -->
                    {% if batch_info %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-warning">
                                    <h4 class="card-title text-white">食材批次信息</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>批次号</th>
                                                    <th>食材</th>
                                                    <th>供应商</th>
                                                    <th>生产日期</th>
                                                    <th>过期日期</th>
                                                    <th>使用数量</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for info in batch_info %}
                                                <tr>
                                                    <td>{{ info.batch.batch_number }}</td>
                                                    <td>{{ info.ingredient_name }}</td>
                                                    <td>{{ info.batch.supplier.name }}</td>
                                                    <td>{{ info.batch.production_date }}</td>
                                                    <td>{{ info.batch.expiry_date }}</td>
                                                    <td>{{ info.quantity }} {{ info.unit }}</td>
                                                    <td>
                                                        <a href="{{ url_for('traceability.trace_batch', batch_number=info.batch.batch_number) }}" class="btn btn-info btn-sm">
                                                            <i class="fas fa-search"></i> 溯源
                                                        </a>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
