{% extends 'base.html' %}

{% block title %}
{% if recipe %}编辑食谱{% else %}添加食谱{% endif %}
{% endblock %}

{% block styles %}
{{ super() }}
<link href="{{ url_for('static', filename='vendor/select2/css/select2.min.css') }}" rel="stylesheet" />
<link href="{{ url_for('static', filename='vendor/select2/css/select2-bootstrap4.min.css') }}" rel="stylesheet" />
<style nonce="{{ csp_nonce }}">
    .ingredient-item {
        position: relative;
        padding: 15px;
        margin-bottom: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background-color: #f9f9f9;
    }
    .remove-btn {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    .preview-image {
        max-height: 200px;
        max-width: 100%;
        margin-top: 10px;
    }
    .user-defined-box {
        border: 2px solid #ffc107; 
        padding: 15px; 
        border-radius: 5px; 
        background-color: #fff8e1;
        margin-bottom: 15px;
    }
    .ingredients-container {
        margin-top: 20px;
    }
    .ingredient-row {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 10px;
        position: relative;
    }
    .ingredient-row .remove-ingredient {
        position: absolute;
        right: 10px;
        top: 10px;
        cursor: pointer;
        color: #dc3545;
    }
    .add-ingredient-btn {
        margin-bottom: 20px;
    }
    .modal-body {
        max-height: 400px;
        overflow-y: auto;
    }
    .ingredient-search {
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% if recipe %}编辑食谱{% else %}添加食谱{% endif %}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('recipe.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" id="recipeForm">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <!-- 基本信息 -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h4 class="mb-0">基本信息</h4>
                            </div>
                            <div class="card-body">
                                <!-- 用户自定义食谱选项 -->
                                <div class="user-defined-box">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="is_user_defined" name="is_user_defined" value="1" {% if recipe and recipe.is_user_defined %}checked{% endif %}>
                                        <label class="form-check-label" for="is_user_defined"><strong>标记为用户自定义食谱</strong></label>
                                    </div>
                                    <small class="form-text text-muted">用户自定义食谱将在菜单规划中优先显示</small>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="name" class="required">食谱名称</label>
                                            <input type="text" class="form-control" id="name" name="name" value="{{ recipe.name if recipe else '' }}" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="category_id">食谱分类</label>
                                            <select class="form-control" id="category_id" name="category_id">
                                                <option value="">-- 请选择分类 --</option>
                                                {% for category in categories %}
                                                <option value="{{ category.id }}" {% if recipe and recipe.category_id == category.id %}selected{% endif %}>
                                                    {{ category.name }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="meal_type">适用餐次</label>
                                            <select class="form-control" id="meal_type" name="meal_type">
                                                <option value="">-- 请选择餐次 --</option>
                                                <option value="早餐" {% if recipe and recipe.meal_type == '早餐' %}selected{% endif %}>早餐</option>
                                                <option value="午餐" {% if recipe and recipe.meal_type == '午餐' %}selected{% endif %}>午餐</option>
                                                <option value="晚餐" {% if recipe and recipe.meal_type == '晚餐' %}selected{% endif %}>晚餐</option>
                                                <option value="早餐,午餐" {% if recipe and recipe.meal_type == '早餐,午餐' %}selected{% endif %}>早餐,午餐</option>
                                                <option value="午餐,晚餐" {% if recipe and recipe.meal_type == '午餐,晚餐' %}selected{% endif %}>午餐,晚餐</option>
                                                <option value="早餐,午餐,晚餐" {% if recipe and recipe.meal_type == '早餐,午餐,晚餐' %}selected{% endif %}>早餐,午餐,晚餐</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="main_image">食谱图片</label>
                                            <div class="input-group">
                                                <div class="custom-file">
                                                    <input type="file" class="custom-file-input" id="main_image" name="main_image" accept="image/*">
                                                    <label class="custom-file-label" for="main_image">选择文件</label>
                                                </div>
                                            </div>
                                            {% if recipe and recipe.main_image %}
                                            <div class="mt-2">
                                                <img src="{{ url_for('static', filename=recipe.main_image) }}" alt="{{ recipe.name }}" class="preview-image">
                                            </div>
                                            {% else %}
                                            <div class="mt-2" id="imagePreview" style="display: none;">
                                                <img src="" alt="预览" class="preview-image">
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="form-group">
                                            <label for="status">状态</label>
                                            <select class="form-control" id="status" name="status">
                                                <option value="1" {% if not recipe or recipe.status == 1 %}selected{% endif %}>启用</option>
                                                <option value="0" {% if recipe and recipe.status == 0 %}selected{% endif %}>停用</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="description">食谱描述</label>
                                            <textarea class="form-control" id="description" name="description" rows="3">{{ recipe.description if recipe else '' }}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 食材配比 -->
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h4 class="mb-0">食材配比</h4>
                            </div>
                            <div class="card-body">
                                <button type="button" class="btn btn-primary add-ingredient-btn" data-toggle="modal" data-target="#ingredientModal">
                                    <i class="fas fa-plus"></i> 添加食材
                                </button>
                                
                                <div id="ingredients-container" class="ingredients-container">
                                    {% if recipe_ingredients %}
                                        {% for ri in recipe_ingredients %}
                                        <div class="ingredient-row" data-id="{{ ri.ingredient_id }}">
                                            <input type="hidden" name="ingredient_ids[]" value="{{ ri.ingredient_id }}">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <strong>{{ ri.ingredient.name }}</strong>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="input-group">
                                                        <input type="number" class="form-control" name="ingredient_quantities[]" value="{{ ri.quantity }}" min="0.01" step="0.01" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <input type="text" class="form-control" name="ingredient_units[]" value="{{ ri.unit }}" required>
                                                </div>
                                                <div class="col-md-2">
                                                    <span class="remove-ingredient"><i class="fas fa-times-circle"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">保存</button>
                            <a href="{{ url_for('recipe.index') }}" class="btn btn-secondary">取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 食材选择模态框 -->
<div class="modal fade" id="ingredientModal" tabindex="-1" role="dialog" aria-labelledby="ingredientModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ingredientModalLabel">选择食材</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="ingredient-search mb-3">
                    <input type="text" class="form-control" id="ingredientSearchInput" placeholder="搜索食材...">
                </div>
                <div id="ingredientsList" class="list-group">
                    <!-- 食材列表将通过AJAX加载 -->
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/select2/select2.min.js') }}"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/bs-custom-file-input/bs-custom-file-input.min.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 初始化文件上传显示文件名
        bsCustomFileInput.init();

        // 图片预览
        $('#main_image').change(function() {
            if (this.files && this.files[0]) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    $('#imagePreview img').attr('src', e.target.result);
                    $('#imagePreview').show();
                }
                reader.readAsDataURL(this.files[0]);
            } else {
                $('#imagePreview').hide();
            }
        });

        // 加载食材列表
        $('#ingredientModal').on('show.bs.modal', function() {
            loadIngredients();
        });

        // 搜索食材
        $('#ingredientSearchInput').on('keyup', function() {
            var searchTerm = $(this).val().toLowerCase();
            $('#ingredientsList .list-group-item').each(function() {
                var ingredientName = $(this).text().toLowerCase();
                if (ingredientName.indexOf(searchTerm) > -1) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });

        // 删除食材
        $(document).on('click', '.remove-ingredient', function() {
            $(this).closest('.ingredient-row').remove();
        });

        // 表单提交前验证
        $('#recipeForm').submit(function(e) {
            e.preventDefault(); // 阻止默认提交

            // 检查必填字段
            var isValid = true;
            $('#recipeForm [required]').each(function() {
                if (!$(this).val()) {
                    isValid = false;
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            if (!isValid) {
                toastr.error('请填写所有必填字段');
                return false;
            }

            // 显示加载状态
            var submitBtn = $('#recipeForm button[type="submit"]');
            var originalText = submitBtn.html();
            submitBtn.html('<i class="fas fa-spinner fa-spin"></i> 保存中...').prop('disabled', true);

            // 使用FormData提交表单（支持文件上传）
            var formData = new FormData(this);

            $.ajax({
                url: $(this).attr('action') || window.location.href,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                },
                success: function(response) {
                    toastr.success('食谱保存成功！');
                    setTimeout(function() {
                        window.location.href = '{{ url_for("recipe.view", id=recipe.id if recipe else 0) }}'.replace('0', response.id || '{{ recipe.id if recipe else 0 }}');
                    }, 1000);
                },
                error: function(xhr, status, error) {
                    // 恢复按钮状态
                    submitBtn.html(originalText).prop('disabled', false);
                    toastr.error(xhr.responseJSON?.message || '保存失败，请稍后重试！');
                }
            });

            return false;
        });

        // 加载食材列表
        function loadIngredients() {
            $.ajax({
                url: '/ingredient/api',
                type: 'GET',
                success: function(data) {
                    var ingredientsList = $('#ingredientsList');
                    ingredientsList.empty();
                    
                    if (data.length === 0) {
                        ingredientsList.html('<div class="alert alert-info">没有找到食材</div>');
                        return;
                    }
                    
                    $.each(data, function(index, ingredient) {
                        var item = $('<a href="#" class="list-group-item list-group-item-action"></a>')
                            .text(ingredient.name)
                            .attr('data-id', ingredient.id)
                            .attr('data-name', ingredient.name)
                            .attr('data-unit', ingredient.unit);
                            
                        item.click(function(e) {
                            e.preventDefault();
                            addIngredient(ingredient);
                            $('#ingredientModal').modal('hide');
                        });
                        
                        ingredientsList.append(item);
                    });
                },
                error: function() {
                    $('#ingredientsList').html('<div class="alert alert-danger">加载食材失败</div>');
                }
            });
        }

        // 添加食材到表单
        function addIngredient(ingredient) {
            // 检查是否已添加该食材
            if ($('#ingredients-container .ingredient-row[data-id="' + ingredient.id + '"]').length > 0) {
                toastr.warning('该食材已添加');
                return;
            }
            
            var ingredientRow = $('<div class="ingredient-row" data-id="' + ingredient.id + '"></div>');
            var html = '<input type="hidden" name="ingredient_ids[]" value="' + ingredient.id + '">' +
                       '<div class="row">' +
                       '  <div class="col-md-4"><strong>' + ingredient.name + '</strong></div>' +
                       '  <div class="col-md-3">' +
                       '    <div class="input-group">' +
                       '      <input type="number" class="form-control" name="ingredient_quantities[]" value="1" min="0.01" step="0.01" required>' +
                       '    </div>' +
                       '  </div>' +
                       '  <div class="col-md-3">' +
                       '    <input type="text" class="form-control" name="ingredient_units[]" value="' + ingredient.unit + '" required>' +
                       '  </div>' +
                       '  <div class="col-md-2">' +
                       '    <span class="remove-ingredient"><i class="fas fa-times-circle"></i></span>' +
                       '  </div>' +
                       '</div>';
            
            ingredientRow.html(html);
            $('#ingredients-container').append(ingredientRow);
        }
    });
</script>
{% endblock %}
