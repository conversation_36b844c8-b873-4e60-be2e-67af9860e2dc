{% extends 'base.html' %}

{% block title %}编辑培训记录{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .photo-preview {
        max-width: 100%;
        max-height: 200px;
        margin-top: 10px;
    }
    .photo-gallery {
        display: flex;
        flex-wrap: wrap;
        margin: -5px;
    }
    .photo-item {
        width: 200px;
        margin: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
    }
    .photo-item img {
        width: 100%;
        height: 150px;
        object-fit: cover;
    }
    .photo-caption {
        padding: 8px;
        background-color: #f8f9fc;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">编辑培训记录</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">培训信息</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                {{ csrf_token() }}
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="training_topic">培训主题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="training_topic" name="training_topic" value="{{ training.training_topic }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="trainer">培训讲师 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="trainer" name="trainer" value="{{ training.trainer }}" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="training_date">培训日期 <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="training_date" name="training_date" value="{{ training.training_time|format_datetime('%Y-%m-%d') }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="training_time">培训时间 <span class="text-danger">*</span></label>
                            <input type="time" class="form-control" id="training_time" name="training_time" value="{{ training.training_time|format_datetime('%H:%M') }}" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="location">培训地点</label>
                            <input type="text" class="form-control" id="location" name="location" value="{{ training.location or '' }}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="duration">培训时长(分钟)</label>
                            <input type="number" class="form-control" id="duration" name="duration" min="0" value="{{ training.duration or '' }}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="attendees_count">参训人数</label>
                            <input type="number" class="form-control" id="attendees_count" name="attendees_count" min="0" value="{{ training.attendees_count or '' }}">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="content_summary">培训内容摘要</label>
                    <textarea class="form-control" id="content_summary" name="content_summary" rows="3">{{ training.content_summary or '' }}</textarea>
                </div>

                <div class="form-group">
                    <label for="effectiveness_evaluation">效果评估</label>
                    <textarea class="form-control" id="effectiveness_evaluation" name="effectiveness_evaluation" rows="3">{{ training.effectiveness_evaluation or '' }}</textarea>
                </div>

                <!-- 现有照片 -->
                <div class="form-group">
                    <label>现有照片</label>
                    <div class="photo-gallery">
                        {% set photos = training.photos %}
                        {% if photos %}
                            {% for photo in photos %}
                                <div class="photo-item">
                                    <a href="{{ photo.file_path }}" target="_blank">
                                        <img src="{{ photo.file_path }}" alt="培训照片">
                                    </a>
                                    <div class="photo-caption">
                                        {{ photo.description or '无描述' }}
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">暂无照片</p>
                        {% endif %}
                    </div>
                </div>

                <div class="form-group">
                    <label for="photos">上传新照片</label>
                    <input type="file" class="form-control-file" id="photos" name="photos" multiple accept="image/*">
                    <small class="form-text text-muted">可以选择多张照片上传，支持jpg、jpeg、png格式</small>
                    <div id="photo-previews" class="mt-2 d-flex flex-wrap"></div>
                </div>

                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">保存</button>
                    <a href="{{ url_for('daily_management.view_training', training_id=training.id) }}" class="btn btn-secondary">取消</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 照片预览
    document.getElementById('photos').addEventListener('change', function(e) {
        const previewsDiv = document.getElementById('photo-previews');
        previewsDiv.innerHTML = '';

        for (const file of this.files) {
            const reader = new FileReader();
            reader.onload = function(event) {
                const img = document.createElement('img');
                img.src = event.target.result;
                img.className = 'photo-preview mr-2 mb-2';
                previewsDiv.appendChild(img);
            }
            reader.readAsDataURL(file);
        }
    });
</script>
{% endblock %}
