{% extends 'base.html' %}

{% block title %}批次详情{% endblock %}

{% block extra_css %}
<style nonce="{{ csp_nonce }}">
    /* 文档操作按钮样式 */
    .document-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 3px;
        align-items: center;
        justify-content: flex-start;
        max-width: 100px; /* 限制最大宽度，确保自动换行 */
    }

    .document-actions .btn {
        width: 28px;
        height: 28px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        font-size: 12px;
        border-width: 1px;
        flex-shrink: 0; /* 防止按钮被压缩 */
    }

    .document-actions .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* 响应式调整 */
    @media (max-width: 1200px) {
        .document-actions {
            max-width: 90px;
        }
    }

    @media (max-width: 992px) {
        .document-actions {
            max-width: 80px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">批次详情</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('material_batch.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                        <a href="{{ url_for('batch_flow.create', batch_id=batch.id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-exchange-alt"></i> 添加流水
                        </a>
                        <a href="{{ url_for('trace_document.upload', batch_id=batch.id) }}" class="btn btn-success btn-sm">
                            <i class="fas fa-file-upload"></i> 上传文档
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">基本信息</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th class="w-30">批次号</th>
                                            <td>{{ batch.batch_number }}</td>
                                        </tr>
                                        <tr>
                                            <th>食材</th>
                                            <td>{{ batch.ingredient.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>供应商</th>
                                            <td>{{ batch.supplier.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>生产日期</th>
                                            <td>{{ batch.production_date }}</td>
                                        </tr>
                                        <tr>
                                            <th>过期日期</th>
                                            <td>{{ batch.expiry_date }}</td>
                                        </tr>
                                        <tr>
                                            <th>生产批号</th>
                                            <td>{{ batch.production_batch_no or '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>产地</th>
                                            <td>{{ batch.origin_place or '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>检验编号</th>
                                            <td>{{ batch.inspection_no or '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>合格证编号</th>
                                            <td>{{ batch.certificate_no or '无' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">库存信息</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th class="w-30">初始数量</th>
                                            <td>{{ batch.initial_quantity }} {{ batch.unit }}</td>
                                        </tr>
                                        <tr>
                                            <th>当前库存</th>
                                            <td>{{ batch.current_quantity }} {{ batch.unit }}</td>
                                        </tr>
                                        <tr>
                                            <th>单价</th>
                                            <td>{{ batch.unit_price or '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>状态</th>
                                            <td>
                                                {% if batch.status == '正常' %}
                                                <span class="badge badge-success">正常</span>
                                                {% elif batch.status == '预警' %}
                                                <span class="badge badge-warning">预警</span>
                                                {% elif batch.status == '过期' %}
                                                <span class="badge badge-danger">过期</span>
                                                {% elif batch.status == '已用完' %}
                                                <span class="badge badge-secondary">已用完</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>所属区域</th>
                                            <td>{{ batch.area.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>备注</th>
                                            <td>{{ batch.remark or '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>创建时间</th>
                                            <td>{{ batch.created_at }}</td>
                                        </tr>
                                        <tr>
                                            <th>更新时间</th>
                                            <td>{{ batch.updated_at }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 溯源文档 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">溯源文档</h4>
                                    <div class="card-tools">
                                        <a href="{{ url_for('trace_document.upload', batch_id=batch.id) }}" class="btn btn-success btn-sm">
                                            <i class="fas fa-file-upload"></i> 上传文档
                                        </a>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>文档类型</th>
                                                    <th>文档编号</th>
                                                    <th>上传时间</th>
                                                    <th>上传人</th>
                                                    <th>备注</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for document in documents %}
                                                <tr>
                                                    <td>{{ document.document_type }}</td>
                                                    <td>{{ document.document_no or '无' }}</td>
                                                    <td>{{ document.upload_time }}</td>
                                                    <td>{{ document.uploader.real_name or document.uploader.username }}</td>
                                                    <td>{{ document.remark or '无' }}</td>
                                                    <td>
                                                        <div class="document-actions">
                                                            <a href="{{ url_for('static', filename=document.document_path) }}" target="_blank" class="btn btn-outline-info" title="查看文档">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-outline-danger delete-document" data-id="{{ document.id }}" title="删除文档">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                                {% else %}
                                                <tr>
                                                    <td colspan="6" class="text-center">暂无文档</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 批次流水 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">批次流水</h4>
                                    <div class="card-tools">
                                        <a href="{{ url_for('batch_flow.create', batch_id=batch.id) }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-plus"></i> 添加流水
                                        </a>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>流水类型</th>
                                                    <th>流向</th>
                                                    <th>数量</th>
                                                    <th>单位</th>
                                                    <th>操作人</th>
                                                    <th>流水日期</th>
                                                    <th>备注</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for flow in flows %}
                                                <tr>
                                                    <td>{{ flow.flow_type }}</td>
                                                    <td>
                                                        {% if flow.flow_direction == '增加' %}
                                                        <span class="text-success">增加</span>
                                                        {% else %}
                                                        <span class="text-danger">减少</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ flow.quantity }}</td>
                                                    <td>{{ flow.unit }}</td>
                                                    <td>{{ flow.operator.real_name or flow.operator.username }}</td>
                                                    <td>{{ flow.flow_date }}</td>
                                                    <td>{{ flow.remark or '无' }}</td>
                                                </tr>
                                                {% else %}
                                                <tr>
                                                    <td colspan="7" class="text-center">暂无流水记录</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(function() {
        // 删除文档
        $('.delete-document').click(function() {
            var id = $(this).data('id');
            if (confirm('确定要删除该文档吗？')) {
                $.ajax({
                    url: "{{ url_for('trace_document.delete', id=0) }}".replace('0', id),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            alert(response.message);
                            location.reload();
                        } else {
                            alert(response.message);
                        }
                    },
                    error: function() {
                        alert('操作失败，请重试');
                    }
                });
            }
        });
    });
</script>
{% endblock %}
