{% extends 'base_public.html' %}

{% block title %}评价{{ inspection_type_name }}照片{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    body {
        background-color: #f8f9fc;
    }

    .header-banner {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        padding: 20px 0;
        margin-bottom: 30px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .school-logo {
        max-height: 80px;
        margin-right: 15px;
    }

    .platform-name {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .school-name {
        font-size: 1.2rem;
    }

    .photo-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
        background-color: white;
    }

    .photo-card .card-header {
        background-color: #4e73df;
        color: white;
        font-weight: bold;
        padding: 15px;
    }

    .photo-card .card-body {
        padding: 20px;
    }

    .photo-container {
        position: relative;
        margin-bottom: 15px;
    }

    .inspection-photo {
        width: 100%;
        border-radius: 5px;
        cursor: pointer;
    }

    .photo-description {
        margin-top: 10px;
        font-style: italic;
        color: #5a5c69;
    }

    .rating-container {
        margin-top: 15px;
        padding: 15px;
        background-color: #f8f9fc;
        border-radius: 5px;
    }

    .evaluation-form h6 {
        text-align: center;
        margin-bottom: 15px;
        color: #5a5c69;
    }

    .star-rating-section .form-label {
        font-weight: 600;
        color: #5a5c69;
        margin-bottom: 8px;
    }

    .star-rating {
        display: flex;
        justify-content: center;
        margin-bottom: 10px;
    }

    .star-rating .star {
        font-size: 2rem;
        color: #dddfeb;
        cursor: pointer;
        margin: 0 5px;
        transition: color 0.2s;
    }

    .star-rating .star:hover,
    .star-rating .star.active {
        color: #f6c23e;
    }

    .rating-label {
        text-align: center;
        font-weight: bold;
        color: #5a5c69;
        margin-bottom: 15px;
    }

    .comment-section .form-label {
        font-weight: 600;
        color: #5a5c69;
        margin-bottom: 8px;
    }

    .comment-input {
        border: 2px solid #e3e6f0;
        border-radius: 8px;
        padding: 12px;
        font-size: 14px;
        transition: border-color 0.3s ease;
    }

    .comment-input:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }

    .submit-evaluation-btn {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        width: 100%;
    }

    .submit-evaluation-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .current-rating {
        margin-top: 15px;
    }

    .star-rating {
        display: flex;
        justify-content: center;
        margin-bottom: 10px;
    }

    .star-rating .star {
        font-size: 2rem;
        color: #dddfeb;
        cursor: pointer;
        margin: 0 5px;
        transition: color 0.2s;
    }

    .star-rating .star.active {
        color: #f6c23e;
    }

    .rating-label {
        text-align: center;
        font-weight: bold;
        color: #5a5c69;
    }

    .submit-rating {
        background-color: #4e73df;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        font-weight: bold;
        margin-top: 15px;
        width: 100%;
    }

    .submit-rating:hover {
        background-color: #2e59d9;
    }

    .success-message {
        display: none;
        background-color: #1cc88a;
        color: white;
        padding: 15px;
        border-radius: 5px;
        margin-top: 15px;
        text-align: center;
        font-weight: bold;
    }

    .error-message {
        display: none;
        background-color: #e74a3b;
        color: white;
        padding: 15px;
        border-radius: 5px;
        margin-top: 15px;
        text-align: center;
        font-weight: bold;
    }

    .modal-photo {
        max-width: 100%;
        max-height: 80vh;
    }

    .no-photos {
        text-align: center;
        padding: 50px 0;
        color: #5a5c69;
    }

    .footer {
        background-color: #f8f9fc;
        padding: 20px 0;
        margin-top: 30px;
        text-align: center;
        color: #5a5c69;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="header-banner">
    <div class="container">
        <div class="d-flex align-items-center">
            <img src="/static/img/logo.png" alt="Logo" class="school-logo">
            <div>
                <div class="platform-name">校园餐智慧食堂</div>
                <div class="school-name">{{ school.name }}</div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-12 mb-4">
            <h2 class="text-center">{{ school.name }} - {{ log.log_date.strftime('%Y-%m-%d') }} {{ inspection_type_name }}照片评价</h2>
            <p class="text-center text-muted">请为以下检查照片进行评分，您的评价将帮助我们改进食堂管理</p>
        </div>
    </div>

    {% if photos_by_item %}
        {% for item, photos in photos_by_item.items() %}
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="photo-card">
                        <div class="card-header">
                            <i class="fas fa-clipboard-check mr-2"></i> {{ item }}
                        </div>
                        <div class="card-body">
                            {% for photo in photos %}
                                <div class="photo-container" id="photo-container-{{ photo.id }}">
                                    <img src="{{ photo.file_path }}" class="inspection-photo"
                                         alt="{{ item }}" data-toggle="modal" data-target="#photoModal"
                                         data-photo-src="{{ photo.file_path }}" data-photo-item="{{ item }}">

                                    {% if photo.description %}
                                        <div class="photo-description">{{ photo.description }}</div>
                                    {% endif %}

                                    <div class="rating-container">
                                        <div class="evaluation-form" data-photo-id="{{ photo.id }}">
                                            <h6 class="mb-3">请对这张照片进行评价：</h6>

                                            <!-- 星级评分 -->
                                            <div class="star-rating-section mb-3">
                                                <label class="form-label">星级评分：</label>
                                                <div class="star-rating" data-photo-id="{{ photo.id }}">
                                                    {% for i in range(1, 6) %}
                                                        <i class="fas fa-star star" data-rating="{{ i }}"></i>
                                                    {% endfor %}
                                                </div>
                                                <div class="rating-label" id="rating-label-{{ photo.id }}">请选择评分</div>
                                            </div>

                                            <!-- 文字评价 -->
                                            <div class="comment-section mb-3">
                                                <label class="form-label">具体评价和改善建议：</label>
                                                <textarea class="form-control comment-input"
                                                         id="comment-{{ photo.id }}"
                                                         rows="4"
                                                         placeholder="请写下您的具体评价和改善建议...&#10;例如：&#10;• 卫生状况良好，餐具摆放整齐&#10;• 建议：地面需要进一步清洁，注意角落卫生&#10;• 工作人员操作规范，值得表扬"></textarea>
                                                <small class="text-muted">
                                                    <i class="fas fa-lightbulb"></i>
                                                    请具体指出优点和需要改进的地方，您的建议将帮助我们提升食堂管理水平
                                                </small>
                                            </div>

                                            <!-- 提交按钮 -->
                                            <div class="submit-section">
                                                <button class="btn btn-primary submit-evaluation-btn" data-photo-id="{{ photo.id }}">
                                                    <i class="fas fa-paper-plane"></i> 提交评价
                                                </button>
                                            </div>
                                        </div>

                                        <!-- 评价结果显示 -->
                                        <div class="rating-result" id="rating-result-{{ photo.id }}" style="display: none;">
                                            <div class="alert alert-success">
                                                <i class="fas fa-check-circle"></i> 评价已提交，谢谢您的反馈！
                                            </div>
                                        </div>

                                        <!-- 当前评价显示 -->
                                        {% if photo.rating %}
                                        <div class="current-rating">
                                            <div class="card border-info">
                                                <div class="card-header bg-info text-white">
                                                    <small><i class="fas fa-comment-dots"></i> 已有评价</small>
                                                </div>
                                                <div class="card-body">
                                                    <div class="rating-display mb-2">
                                                        <span class="me-2">评分：</span>
                                                        {% for i in range(1, 6) %}
                                                            {% if i <= photo.rating %}
                                                                <i class="fas fa-star text-warning"></i>
                                                            {% else %}
                                                                <i class="far fa-star text-muted"></i>
                                                            {% endif %}
                                                        {% endfor %}
                                                        <span class="ms-2 badge bg-primary">{{ photo.rating }}/5</span>
                                                    </div>
                                                    {% if photo.comment %}
                                                    <div class="comment-display">
                                                        <small class="text-muted"><i class="fas fa-quote-left"></i> 评价内容：</small>
                                                        <div class="mt-1 p-2 bg-light rounded">
                                                            {{ photo.comment|replace('\n', '<br>')|safe }}
                                                        </div>
                                                    </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                {% if not loop.last %}<hr>{% endif %}
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    {% else %}
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="photo-card">
                    <div class="card-body">
                        <div class="no-photos">
                            <i class="fas fa-image fa-3x mb-3"></i>
                            <h4>暂无照片</h4>
                            <p>当前检查记录暂无照片可供评价</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- 照片查看模态框 -->
<div class="modal fade" id="photoModal" tabindex="-1" role="dialog" aria-labelledby="photoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="photoModalLabel">检查照片</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img src="" class="modal-photo" id="modalPhoto">
            </div>
        </div>
    </div>
</div>

<div class="footer">
    <div class="container">
        <p>© {{ now.year }} 校园餐智慧食堂 - 版权所有</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 照片模态框
    $('#photoModal').on('show.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const photoSrc = button.data('photo-src');
        const photoItem = button.data('photo-item');

        const modal = $(this);
        modal.find('.modal-title').text(photoItem + ' - 照片查看');
        modal.find('#modalPhoto').attr('src', photoSrc);
    });

    // 星级评分点击事件
    $('.star').on('click', function() {
        const rating = $(this).data('rating');
        const starRating = $(this).parent();
        const photoId = starRating.data('photo-id');

        // 更新星星显示
        starRating.find('.star').removeClass('active');
        starRating.find('.star').each(function(index) {
            if (index < rating) {
                $(this).addClass('active');
            }
        });

        // 更新评分标签
        $(`#rating-label-${photoId}`).text(`当前评分: ${rating} 星`);
    });

    // 鼠标悬停效果
    $('.star').on('mouseenter', function() {
        const rating = $(this).data('rating');
        const starRating = $(this).parent();

        starRating.find('.star').each(function(index) {
            if (index < rating) {
                $(this).addClass('hover');
            } else {
                $(this).removeClass('hover');
            }
        });
    }).on('mouseleave', function() {
        $(this).parent().find('.star').removeClass('hover');
    });

    // 提交评价按钮点击事件
    $('.submit-evaluation-btn').on('click', function() {
        const photoId = $(this).data('photo-id');
        const rating = $(this).closest('.evaluation-form').find('.star.active').length;
        const comment = $(`#comment-${photoId}`).val().trim();

        if (rating === 0) {
            alert('请选择星级评分');
            return;
        }

        if (!comment) {
            alert('请填写具体评价和改善建议');
            return;
        }

        // 禁用按钮防止重复点击
        $(this).prop('disabled', true);
        $(this).text('提交中...');

        // 发送评分请求
        fetch('/daily-management/api/v2/photos/public/rate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                photo_id: photoId,
                rating: rating,
                comment: comment
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 隐藏评价表单，显示成功信息
                $(`#photo-container-${photoId} .evaluation-form`).hide();
                $(`#rating-result-${photoId}`).show();

                // 创建并显示当前评价
                const starsHtml = Array.from({length: 5}, (_, i) => {
                    const starClass = i < rating ? 'fas fa-star text-warning' : 'far fa-star text-muted';
                    return `<i class="${starClass}"></i>`;
                }).join('');

                const currentRatingHtml = `
                    <div class="current-rating">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <small><i class="fas fa-comment-dots"></i> 已有评价</small>
                            </div>
                            <div class="card-body">
                                <div class="rating-display mb-2">
                                    <span class="me-2">评分：</span>
                                    ${starsHtml}
                                    <span class="ms-2 badge bg-primary">${rating}/5</span>
                                </div>
                                <div class="comment-display">
                                    <small class="text-muted"><i class="fas fa-quote-left"></i> 评价内容：</small>
                                    <div class="mt-1 p-2 bg-light rounded">
                                        ${comment.replace(/\n/g, '<br>')}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                $(`#photo-container-${photoId} .rating-container`).append(currentRatingHtml);

            } else {
                alert(data.error || '评价提交失败，请重试');
                // 重新启用按钮
                $(this).prop('disabled', false);
                $(this).html('<i class="fas fa-paper-plane"></i> 提交评价');
            }
        })
        .catch(error => {
            alert('评价提交失败，请重试');
            console.error('Error:', error);
            // 重新启用按钮
            $(this).prop('disabled', false);
            $(this).html('<i class="fas fa-paper-plane"></i> 提交评价');
        });
    });
</script>
{% endblock %}
