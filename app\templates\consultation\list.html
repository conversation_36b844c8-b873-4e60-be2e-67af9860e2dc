{% extends "base.html" %}

{% block title %}在线咨询管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">在线咨询管理</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('consultation.export_consultations') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-download"></i> 导出数据
                        </a>
                    </div>
                </div>
                
                <!-- 统计信息 -->
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-comments"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">总咨询数</span>
                                    <span class="info-box-number">{{ consultations.total }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning"><i class="fas fa-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">待处理</span>
                                    <span class="info-box-number">{{ stats.status_stats.get('待处理', 0) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">已回复</span>
                                    <span class="info-box-number">{{ stats.status_stats.get('已回复', 0) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-secondary"><i class="fas fa-times"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">已关闭</span>
                                    <span class="info-box-number">{{ stats.status_stats.get('已关闭', 0) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 搜索表单 -->
                    <form method="GET" class="mb-3">
                        <div class="row">
                            <div class="col-md-3">
                                {{ form.keyword.label(class="form-label") }}
                                {{ form.keyword(class="form-control") }}
                            </div>
                            <div class="col-md-2">
                                {{ form.status.label(class="form-label") }}
                                {{ form.status(class="form-control") }}
                            </div>
                            <div class="col-md-2">
                                {{ form.contact_type.label(class="form-label") }}
                                {{ form.contact_type(class="form-control") }}
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    {{ form.submit(class="btn btn-primary") }}
                                    <a href="{{ url_for('consultation.consultation_list') }}" class="btn btn-secondary">重置</a>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <!-- 咨询列表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>姓名</th>
                                    <th>联系方式</th>
                                    <th>咨询内容</th>
                                    <th>状态</th>
                                    <th>提交时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for consultation in consultations.items %}
                                <tr>
                                    <td>{{ consultation.id }}</td>
                                    <td>{{ consultation.name }}</td>
                                    <td>
                                        <span class="badge badge-info">{{ consultation.contact_type }}</span><br>
                                        {{ consultation.contact_value }}
                                    </td>
                                    <td>
                                        <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                                            {{ consultation.content[:50] }}{% if consultation.content|length > 50 %}...{% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if consultation.status == '待处理' %}
                                            <span class="badge badge-warning">{{ consultation.status }}</span>
                                        {% elif consultation.status == '已回复' %}
                                            <span class="badge badge-success">{{ consultation.status }}</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{{ consultation.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ consultation.created_at|format_datetime }}</td>
                                    <td>
                                        <a href="{{ url_for('consultation.consultation_detail', id=consultation.id) }}" 
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">暂无咨询记录</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    {% if consultations.pages > 1 %}
                    <nav aria-label="咨询列表分页">
                        <ul class="pagination justify-content-center">
                            {% if consultations.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('consultation.consultation_list', page=consultations.prev_num, **request.args) }}">上一页</a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in consultations.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != consultations.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('consultation.consultation_list', page=page_num, **request.args) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">…</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if consultations.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('consultation.consultation_list', page=consultations.next_num, **request.args) }}">下一页</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
