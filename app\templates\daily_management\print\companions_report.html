{% extends 'print_base.html' %}

{% block title %}陪餐记录汇总{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .report-header {
        text-align: center;
        margin-bottom: 20px;
    }
    
    .report-header h1 {
        font-size: 24px;
        margin-bottom: 10px;
    }
    
    .report-header p {
        font-size: 14px;
        color: #666;
        margin-bottom: 5px;
    }
    
    .report-meta {
        margin-bottom: 20px;
        font-size: 14px;
    }
    
    .report-meta-item {
        margin-bottom: 5px;
    }
    
    .report-meta-label {
        font-weight: bold;
        display: inline-block;
        width: 100px;
    }
    
    .companion-section {
        margin-bottom: 30px;
        page-break-inside: avoid;
    }
    
    .companion-header {
        font-size: 18px;
        margin-bottom: 10px;
        padding-bottom: 5px;
        border-bottom: 1px solid #ddd;
    }
    
    .companion-meta {
        margin-bottom: 15px;
    }
    
    .companion-meta-row {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 5px;
    }
    
    .companion-meta-item {
        flex: 1;
        min-width: 200px;
        margin-bottom: 5px;
    }
    
    .companion-meta-label {
        font-weight: bold;
        margin-right: 5px;
    }
    
    .rating-display {
        display: inline-block;
    }
    
    .rating-display i {
        color: #f6c23e;
        margin-right: 2px;
    }
    
    .feedback-section {
        margin-top: 10px;
    }
    
    .feedback-label {
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .feedback-content {
        padding: 10px;
        background-color: #f9f9f9;
        border: 1px solid #eee;
        border-radius: 4px;
    }
    
    .no-records {
        text-align: center;
        padding: 20px;
        background-color: #f9f9f9;
        border-radius: 4px;
        font-style: italic;
        color: #666;
    }
    
    .summary-section {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #ddd;
    }
    
    .summary-title {
        font-size: 18px;
        margin-bottom: 10px;
    }
    
    .summary-content {
        display: flex;
        flex-wrap: wrap;
    }
    
    .summary-item {
        flex: 1;
        min-width: 150px;
        margin-bottom: 10px;
        text-align: center;
    }
    
    .summary-label {
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .summary-value {
        font-size: 18px;
    }
    
    .print-footer {
        margin-top: 30px;
        text-align: right;
        font-size: 12px;
        color: #666;
    }
    
    @media print {
        body {
            font-size: 12px;
        }
        
        .report-header h1 {
            font-size: 20px;
        }
        
        .companion-header {
            font-size: 16px;
        }
        
        .summary-title {
            font-size: 16px;
        }
        
        .summary-value {
            font-size: 16px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="report-header">
    <h1>{{ log.area.name if log.area else '学校' }} - 陪餐记录汇总</h1>
    <p>日期：{{ log.log_date }}</p>
</div>

<div class="report-meta">
    <div class="report-meta-item">
        <span class="report-meta-label">管理员：</span>
        <span>{{ log.manager or '未设置' }}</span>
    </div>
    <div class="report-meta-item">
        <span class="report-meta-label">就餐人数：</span>
        <span>学生 {{ log.student_count or 0 }} 人，教师 {{ log.teacher_count or 0 }} 人，其他 {{ log.other_count or 0 }} 人</span>
    </div>
</div>

{% if companions %}
    <div class="summary-section">
        <div class="summary-title">陪餐统计</div>
        <div class="summary-content">
            <div class="summary-item">
                <div class="summary-label">陪餐总人数</div>
                <div class="summary-value">{{ companions|length }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">早餐陪餐</div>
                <div class="summary-value">{{ companions|selectattr('meal_type', 'equalto', 'breakfast')|list|length }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">午餐陪餐</div>
                <div class="summary-value">{{ companions|selectattr('meal_type', 'equalto', 'lunch')|list|length }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">晚餐陪餐</div>
                <div class="summary-value">{{ companions|selectattr('meal_type', 'equalto', 'dinner')|list|length }}</div>
            </div>
        </div>
    </div>

    {% for companion in companions %}
    <div class="companion-section">
        <div class="companion-header">陪餐记录 #{{ loop.index }}</div>
        
        <div class="companion-meta">
            <div class="companion-meta-row">
                <div class="companion-meta-item">
                    <span class="companion-meta-label">陪餐人：</span>
                    <span>{{ companion.companion_name }}</span>
                </div>
                <div class="companion-meta-item">
                    <span class="companion-meta-label">职务：</span>
                    <span>{{ companion.position or '未设置' }}</span>
                </div>
            </div>
            
            <div class="companion-meta-row">
                <div class="companion-meta-item">
                    <span class="companion-meta-label">陪餐时间：</span>
                    <span>{{ companion.dining_time|format_datetime if companion.dining_time else '未设置' }}</span>
                </div>
                <div class="companion-meta-item">
                    <span class="companion-meta-label">陪餐地点：</span>
                    <span>{{ companion.dining_location or '未设置' }}</span>
                </div>
            </div>
            
            <div class="companion-meta-row">
                <div class="companion-meta-item">
                    <span class="companion-meta-label">就餐人数：</span>
                    <span>{{ companion.dining_count or 1 }} 人</span>
                </div>
                <div class="companion-meta-item">
                    <span class="companion-meta-label">餐次：</span>
                    <span>
                        {% if companion.meal_type == 'breakfast' %}
                            早餐
                        {% elif companion.meal_type == 'lunch' %}
                            午餐
                        {% elif companion.meal_type == 'dinner' %}
                            晚餐
                        {% else %}
                            {{ companion.meal_type }}
                        {% endif %}
                    </span>
                </div>
            </div>
            
            <div class="companion-meta-row">
                <div class="companion-meta-item">
                    <span class="companion-meta-label">菜品评价：</span>
                    <span class="rating-display">
                        {% for i in range(5) %}
                            {% if i < companion.food_rating %}
                            <i class="fas fa-star"></i>
                            {% else %}
                            <i class="far fa-star"></i>
                            {% endif %}
                        {% endfor %}
                    </span>
                </div>
                <div class="companion-meta-item">
                    <span class="companion-meta-label">卫生评价：</span>
                    <span class="rating-display">
                        {% for i in range(5) %}
                            {% if i < companion.hygiene_rating %}
                            <i class="fas fa-star"></i>
                            {% else %}
                            <i class="far fa-star"></i>
                            {% endif %}
                        {% endfor %}
                    </span>
                </div>
            </div>
            
            <div class="companion-meta-row">
                <div class="companion-meta-item">
                    <span class="companion-meta-label">服务评价：</span>
                    <span class="rating-display">
                        {% for i in range(5) %}
                            {% if i < companion.service_rating %}
                            <i class="fas fa-star"></i>
                            {% else %}
                            <i class="far fa-star"></i>
                            {% endif %}
                        {% endfor %}
                    </span>
                </div>
            </div>
        </div>
        
        {% if companion.feedback %}
        <div class="feedback-section">
            <div class="feedback-label">意见建议：</div>
            <div class="feedback-content">{{ companion.feedback|nl2br }}</div>
        </div>
        {% endif %}
    </div>
    {% endfor %}
{% else %}
    <div class="no-records">
        <p>暂无陪餐记录</p>
    </div>
{% endif %}

<div class="print-footer">
    <p>打印时间：{{ print_date|format_datetime }}</p>
</div>
{% endblock %}
