{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">选择食材</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('product_batch.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <!-- 步骤进度条 -->
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">创建产品批次流程</small>
                                    <small class="text-muted">步骤 2/5</small>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" role="progressbar" class="w-40" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="d-flex justify-content-between mt-2">
                                    <small class="text-success">1. 基本信息</small>
                                    <small class="text-success font-weight-bold">2. 选择食材</small>
                                    <small class="text-muted">3. 设置属性</small>
                                    <small class="text-muted">4. 个性调整</small>
                                    <small class="text-muted">5. 确认创建</small>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <h5><i class="icon fas fa-info"></i> 批次信息</h5>
                                <p>批次名称：{{ batch.name }}</p>
                                <p>分类：{{ batch.category.name if batch.category else '' }}</p>
                                <p>供应商：{{ batch.supplier.name if batch.supplier else '' }}</p>
                            </div>

                            <form method="post" id="ingredientForm">
                                {{ form.csrf_token }}
                                {{ form.batch_id }}

                                <div class="form-group">
                                    <div class="input-group mb-3">
                                        <input type="text" id="searchIngredient" class="form-control" placeholder="搜索食材...">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-12 mb-2">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-default" id="selectAll">全选</button>
                                                <button type="button" class="btn btn-default" id="deselectAll">取消全选</button>
                                            </div>
                                            <span class="ml-3">已选择 <span id="selectedCount">0</span> 个食材</span>
                                        </div>
                                    </div>

                                    <div class="row ingredient-container">
                                        {% for ingredient in ingredients %}
                                        <div class="col-md-3 col-sm-4 col-6 mb-3 ingredient-item">
                                            <div class="card">
                                                <div class="card-body p-2">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox"
                                                               class="custom-control-input"
                                                               id="ingredient_{{ ingredient.id }}"
                                                               name="ingredients"
                                                               value="{{ ingredient.id }}">
                                                        <label class="custom-control-label" for="ingredient_{{ ingredient.id }}">
                                                            {{ ingredient.name }}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>

                                    {% if form.ingredients.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.ingredients.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="form-group text-center">
                                    {{ form.submit(class="btn btn-primary") }}
                                    <a href="{{ url_for('product_batch.create') }}" class="btn btn-default">返回上一步</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 搜索食材
    $('#searchIngredient').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('.ingredient-item').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // 清除搜索
    $('#clearSearch').click(function() {
        $('#searchIngredient').val('');
        $('.ingredient-item').show();
    });

    // 全选
    $('#selectAll').click(function() {
        $('.ingredient-item:visible input[type="checkbox"]').prop('checked', true);
        updateSelectedCount();
    });

    // 取消全选
    $('#deselectAll').click(function() {
        $('.ingredient-item:visible input[type="checkbox"]').prop('checked', false);
        updateSelectedCount();
    });

    // 更新已选择数量
    function updateSelectedCount() {
        var count = $('.ingredient-item input[type="checkbox"]:checked').length;
        $('#selectedCount').text(count);
    }

    // 监听复选框变化
    $('.ingredient-item input[type="checkbox"]').change(function() {
        updateSelectedCount();
    });

    // 初始化已选择数量
    updateSelectedCount();
});
</script>
{% endblock %}
