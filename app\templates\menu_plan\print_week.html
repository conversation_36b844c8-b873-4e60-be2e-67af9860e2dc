<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>周菜单计划打印</title>
    <style nonce="{{ csp_nonce }}">
        body {
            font-family: SimSun, "宋体", "Microsoft YaHei", "微软雅黑", sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12pt;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 15px;
        }
        .title {
            font-size: 20pt;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .subtitle {
            font-size: 16pt;
            margin-bottom: 15px;
        }
        .info {
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
        }
        .info-item {
            margin-bottom: 8px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        th, td {
            border: 1px solid #000;
            padding: 6px;
            vertical-align: top;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
        }
        .date-cell {
            font-weight: bold;
            text-align: center;
            width: 10%;
        }
        .meal-cell {
            width: 30%;
            text-align: left;
        }
        .meal-list {
            text-align: left;
            list-style-type: none;
            padding-left: 0;
            margin: 0;
        }
        .meal-list li {
            margin-bottom: 3px;
            font-size: 10pt;
        }
        .meal-status {
            font-size: 9pt;
            color: #666;
            margin-bottom: 3px;
        }
        .footer {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
        }
        .signature {
            margin-top: 40px;
        }
        @media print {
            body {
                padding: 0;
            }
            @page {
                size: A4 landscape;
                margin: 1cm;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">{{ area.name if area else '岳阳县' }}周菜单计划表</div>
        </div>

        <div class="info">
            <div>
                <div class="info-item"><strong>周期：</strong>{{ week_start_str[:4] }}年{{ week_start_str[5:7] }}月{{ week_start_str[8:10] }}日 至 {{ week_end_str[:4] }}年{{ week_end_str[5:7] }}月{{ week_end_str[8:10] }}日</div>
            </div>
            <div>
                <div class="info-item"><strong>打印日期：</strong>{{  now|format_datetime('%Y年%m月%d日')   }}</div>
            </div>
        </div>

        <table>
            <thead>
                <tr>
                    <th class="date-cell">日期</th>
                    <th class="meal-cell">早餐</th>
                    <th class="meal-cell">午餐</th>
                    <th class="meal-cell">晚餐</th>
                </tr>
            </thead>
            <tbody>
                {% for day_date, day_data in week_data.items() %}
                <tr>
                    <td class="date-cell">
                        {{ day_data.weekday }}<br>
                        {{ day_date.strftime('%m-%d') if day_date is not string else day_date }}
                    </td>

                    {% for meal_type in ['早餐', '午餐', '晚餐'] %}
                    <td class="meal-cell">
                        {% if day_data.meals[meal_type] %}
                            {% set plan = day_data.meals[meal_type] %}
                            <div class="meal-status">
                                状态: {{ plan.status }}
                            </div>

                            <ul class="meal-list">
                                {% for menu_recipe in plan.menu_recipes %}
                                <li>{{ menu_recipe.recipe.name }} ({{ menu_recipe.planned_quantity }})</li>
                                {% endfor %}
                            </ul>

                            {% if plan.menu_recipes.count() > 0 %}
                            <div style="font-size: 9pt; text-align: right;">
                                共{{ plan.menu_recipes.count() }}个菜品
                            </div>
                            {% endif %}
                        {% else %}
                            <div class="text-center">-</div>
                        {% endif %}
                    </td>
                    {% endfor %}
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="footer">
            <table style="width: 100%; border: none;">
                <tr>
                    <td style="width: 33%; border: none; vertical-align: bottom;">
                        <div><strong>制表人：</strong>________________</div>
                    </td>
                    <td style="width: 33%; border: none; vertical-align: bottom; text-align: center;">
                        <div><strong>审核人：</strong>________________</div>
                    </td>
                    <td style="width: 33%; border: none; vertical-align: bottom; text-align: right;">
                        <div><strong>日期：</strong>________________</div>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <script nonce="{{ csp_nonce }}">
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
