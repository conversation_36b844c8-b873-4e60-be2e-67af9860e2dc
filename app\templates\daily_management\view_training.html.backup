{% extends 'base.html' %}

{% block title %}培训记录详情{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .photo-gallery {
        display: flex;
        flex-wrap: wrap;
        margin: -5px;
    }
    .photo-item {
        width: 200px;
        margin: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
    }
    .photo-item img {
        width: 100%;
        height: 150px;
        object-fit: cover;
    }
    .photo-caption {
        padding: 8px;
        background-color: #f8f9fc;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">培训记录详情</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">培训信息</h6>
            <div>
                <a href="{{ url_for('daily_management.edit_training', training_id=training.id) }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-edit mr-1"></i> 编辑
                </a>
                <a href="{{ url_for('daily_management.print_training_detail', training_id=training.id) }}" class="btn btn-info btn-sm" target="_blank">
                    <i class="fas fa-print mr-1"></i> 打印记录
                </a>
                <button type="button" class="btn btn-danger btn-sm" onclick="deleteTraining()">
                    <i class="fas fa-trash mr-1"></i> 删除
                </button>
                <a href="{{ url_for('daily_management.trainings', log_id=training.daily_log_id) }}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-left mr-1"></i> 返回列表
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 30%">培训主题</th>
                            <td>{{ training.training_topic }}</td>
                        </tr>
                        <tr>
                            <th>培训讲师</th>
                            <td>{{ training.trainer }}</td>
                        </tr>
                        <tr>
                            <th>培训时间</th>
                            <td>{{ training.training_time|format_datetime }}</td>
                        </tr>
                        <tr>
                            <th>培训地点</th>
                            <td>{{ training.location or '-' }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 30%">培训时长</th>
                            <td>{{ training.duration or 0 }} 分钟</td>
                        </tr>
                        <tr>
                            <th>参训人数</th>
                            <td>{{ training.attendees_count or 0 }} 人</td>
                        </tr>
                        <tr>
                            <th>创建时间</th>
                            <td>{{ training.created_at|format_datetime }}</td>
                        </tr>
                        <tr>
                            <th>所属日志</th>
                            <td>
                                {% if log %}
                                    <a href="{{ url_for('daily_management.edit_log', date_str=log.log_date.strftime('%Y-%m-%d')) }}">
                                        查看日志
                                    </a>
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <h5 class="font-weight-bold">培训内容摘要</h5>
                    <p class="border rounded p-3 bg-light">{{ training.content_summary or '无' }}</p>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <h5 class="font-weight-bold">效果评估</h5>
                    <p class="border rounded p-3 bg-light">{{ training.effectiveness_evaluation or '无' }}</p>
                </div>
            </div>

            {% if photos %}
            <div class="row mt-4">
                <div class="col-md-12">
                    <h5 class="font-weight-bold">培训照片</h5>
                    <div class="photo-gallery">
                        {% for photo in photos %}
                            <div class="photo-item">
                                <a href="{{ photo.file_path }}" target="_blank">
                                    <img src="{{ photo.file_path }}" alt="培训照片">
                                </a>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 删除确认对话框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要删除这条培训记录吗？此操作不可恢复。</p>
            </div>
            <div class="modal-footer">
                <form action="{{ url_for('daily_management.delete_training', training_id=training.id) }}" method="post">
                    {{ csrf_token() }}
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
function deleteTraining() {
    $('#deleteModal').modal('show');
}
</script>
{% endblock %}
