{% extends 'base.html' %}

{% block title %}周菜单安排{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
.table th, .table td { vertical-align: middle; }
.badge { font-size: 1em; }
.recipe-card { border: 1px solid #eee; border-radius: 5px; padding: 8px; margin-bottom: 8px; cursor: pointer; position: relative; }
.recipe-card.selected { border-color: #007bff; background: #eaf4ff; }
.favorite-btn { position: absolute; top: 8px; right: 8px; color: #ccc; cursor: pointer; }
.favorite-btn.active { color: #dc3545; }
.expand-ingredients { color: #007bff; cursor: pointer; font-size: 0.9em; margin-left: 5px; }
.ingredient-list { display: none; background: #f8f9fa; border-radius: 3px; padding: 8px 10px; margin-top: 5px; font-size: 0.95em; }
.selected-recipe-tag { display: inline-block; background: #f8f9fa; border: 1px solid #ddd; border-radius: 20px; padding: 5px 10px; margin: 5px; cursor: move; position: relative; }
.selected-recipe-tag .move-handle { cursor: move; margin-right: 6px; color: #888; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <h3 class="mb-3">周菜单安排</h3>
  <table class="table table-bordered text-center align-middle">
                            <thead>
                                <tr>
        <th>日期</th>
        <th>早餐</th>
        <th>午餐</th>
        <th>晚餐</th>
                                </tr>
                            </thead>
                            <tbody>
      {% for date, day in week_dates.items() %}
      <tr>
        <td style="width:120px;">{{ day.label }}</td>
        {% for meal in ['早餐', '午餐', '晚餐'] %}
        <td style="min-width:220px;">
          <div id="cell-{{ date }}-{{ meal }}">
            <!-- 菜品名动态填充 -->
                                                    </div>
          <button class="btn btn-sm btn-outline-primary mt-2 edit-menu-btn"
                  data-date="{{ date }}" data-meal="{{ meal }}">
            编辑
                                                            </button>
                                    </td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
  <div class="text-center mt-4">
    <button class="btn btn-primary" id="saveBtn">保存并发布</button>
    <button class="btn btn-secondary" id="saveDraftBtn">保存为草稿</button>
                    </div>
                </div>

<!-- 菜品选择弹窗 -->
<div class="modal fade" id="menuEditModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">选择菜品</h5>
        <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
      </div>
      <div class="modal-body" id="menuEditModalBody">
        <!-- 选择UI由JS动态插入 -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" id="modalConfirmBtn">确定</button>
        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/sortablejs/Sortable.min.js') }}"></script>
<script nonce="{{ csp_nonce }}">
window.menuData = {{ menuData|tojson|safe }};
const weekDates = {{ week_dates|tojson|safe }};
const mealTypes = ['早餐', '午餐', '晚餐'];
const recipesByCategory = {{ recipes_by_category|tojson|safe }};
const favoriteRecipeIds = {{ favorite_recipe_ids|tojson|safe }};

function renderTable() {
  for (const date in weekDates) {
    for (const meal of mealTypes) {
      const cellId = `cell-${date}-${meal}`;
      const recipes = (window.menuData[date] && window.menuData[date][meal]) || [];
      let html = '';
      if (recipes.length === 0) {
        html = '<span class="text-muted">未选择</span>';
      } else {
        html = recipes.map(r => `<span class="badge badge-info mr-1">${r.name}</span>`).join('');
      }
      document.getElementById(cellId).innerHTML = html;
    }
  }
}

function renderRecipeSelector(selectedList) {
  let html = `
    <div class="selected-recipes-container mb-3">
      <h5>已选择的菜品 <span class="badge badge-primary" id="selectedCount">${selectedList.length}</span></h5>
      <div class="selected-recipes" id="selectedRecipes">
        ${selectedList.map(r => `
          <div class="selected-recipe-tag" id="recipe-tag-${r.id}">
            <span class="move-handle"><i class="fas fa-arrows-alt"></i></span>
            ${r.name}
            <button type="button" class="close ml-2 remove-recipe" data-id="${r.id}"><span>&times;</span></button>
          </div>
        `).join('')}
      </div>
    </div>
    <div class="search-box mb-2">
      <input type="text" id="recipeSearch" class="form-control" placeholder="搜索菜品...">
      <span class="clear-search" id="clearSearch" style="position:absolute;right:10px;top:8px;cursor:pointer;"><i class="fas fa-times"></i></span>
    </div>
    <ul class="nav nav-tabs recipe-tabs" id="recipeTabs" role="tablist">
      ${Object.keys(recipesByCategory).map((cat, i) => `
        <li class="nav-item">
          <a class="nav-link ${i===0?'active':''}" id="tab-${cat}" data-toggle="tab" href="#content-${cat}" role="tab">
            ${cat} (${recipesByCategory[cat].length})
          </a>
        </li>
      `).join('')}
    </ul>
    <div class="tab-content p-3" id="recipeTabContent">
      ${Object.entries(recipesByCategory).map(([cat, recipes], i) => `
        <div class="tab-pane fade ${i===0?'show active':''}" id="content-${cat}" role="tabpanel">
          <div class="recipe-grid row">
            ${recipes.map(recipe => `
              <div class="col-md-3 mb-3">
                <div class="recipe-card ${selectedList.find(r=>r.id===recipe.id)?'selected':''}" data-id="${recipe.id}" data-name="${recipe.name}">
                  <div class="favorite-btn ${favoriteRecipeIds.includes(recipe.id)?'active':''}" data-recipe-id="${recipe.id}">
                    <i class="fas fa-heart"></i>
                  </div>
                  <h6 class="mt-2 mb-0">${recipe.name}
                    <span class="expand-ingredients" data-toggle="ingredients" data-recipe-id="${recipe.id}">[参考食材]</span>
                  </h6>
                  <small class="text-muted">${recipe.meal_type||'不限'}</small>
                  <div class="ingredient-list" id="ingredients-${recipe.id}" style="display:none;">
                    ${recipe.suggested_ingredients && recipe.suggested_ingredients.length
                      ? `<ul class="mb-0 pl-3">${recipe.suggested_ingredients.map(ing=>`<li>${ing.ingredient_name} ${ing.quantity}${ing.unit}</li>`).join('')}</ul>`
                      : '<span class="text-muted">无参考食材</span>'}
                  </div>
                </div>
              </div>
            `).join('')}
          </div>
        </div>
      `).join('')}
    </div>
  `;
  $('#menuEditModalBody').html(html);

  new Sortable(document.getElementById('selectedRecipes'), {
    animation: 150,
    handle: '.move-handle',
    draggable: '.selected-recipe-tag'
  });

  $('.favorite-btn').off('click').on('click', function(e) {
    e.stopPropagation();
    const btn = $(this);
    const recipeId = btn.data('recipe-id');
    $.ajax({
      url: '/api/toggle-favorite/' + recipeId,
      type: 'POST',
      success: function(response) {
        if (response.action === 'favorited') {
          btn.addClass('active');
          toastr.success('已收藏');
        } else {
          btn.removeClass('active');
          toastr.info('已取消收藏');
            }
        }
    });
  });

  $('.expand-ingredients').off('click').on('click', function(e) {
    e.stopPropagation();
    const rid = $(this).data('recipe-id');
    $('#ingredients-' + rid).slideToggle(150);
  });

  $('.recipe-card').off('click').on('click', function(e) {
    if ($(e.target).closest('.favorite-btn').length > 0) return;
    const recipeId = $(this).data('id');
    const recipeName = $(this).data('name');
    if ($(this).hasClass('selected')) {
      $(this).removeClass('selected');
      $('#recipe-tag-' + recipeId).remove();
    } else {
      $(this).addClass('selected');
      const tag = $(`<div class="selected-recipe-tag" id="recipe-tag-${recipeId}">
        <span class="move-handle"><i class="fas fa-arrows-alt"></i></span>
        ${recipeName}
        <button type="button" class="close ml-2 remove-recipe" data-id="${recipeId}"><span>&times;</span></button>
      </div>`);
      $('#selectedRecipes').append(tag);
    }
    updateSelectedCount();
  });

  $(document).off('click', '.remove-recipe').on('click', '.remove-recipe', function(e) {
    e.preventDefault();
    e.stopPropagation();
    const recipeId = $(this).data('id');
    $('#recipe-tag-' + recipeId).remove();
    $('.recipe-card[data-id="' + recipeId + '"]').removeClass('selected');
    updateSelectedCount();
  });

  $('#recipeSearch').on('input', function() {
    const searchText = $(this).val().toLowerCase();
    if (searchText) {
      $('.recipe-tabs, .tab-content').hide();
      if ($('#searchResults').length === 0) {
        $('<div id="searchResults"><h5>搜索结果</h5><div class="recipe-grid row"></div></div>').insertAfter('.search-box');
      }
      $('#searchResults .recipe-grid').empty();
      let matchCount = 0;
      $('.recipe-card').each(function() {
        const recipeName = $(this).data('name').toLowerCase();
        if (recipeName.includes(searchText)) {
          const clone = $(this).closest('.col-md-3').clone(true);
          $('#searchResults .recipe-grid').append(clone);
          matchCount++;
        }
      });
      $('#searchResults h5').text('搜索结果 (' + matchCount + ')');
    } else {
      $('.recipe-tabs, .tab-content').show();
      $('#searchResults').remove();
    }
  });
  $('#clearSearch').click(function() {
    $('#recipeSearch').val('').trigger('input');
  });

  function updateSelectedCount() {
    $('#selectedCount').text($('.selected-recipe-tag').length);
  }
}

function getSelectedRecipes() {
  const ids = [];
  $('#selectedRecipes .selected-recipe-tag').each(function() {
    const id = parseInt(this.id.replace('recipe-tag-', ''));
    for (const cat in recipesByCategory) {
      const found = recipesByCategory[cat].find(r => r.id === id);
      if (found) {
        ids.push({id: found.id, name: found.name, quantity: 1});
        break;
      }
    }
  });
  return ids;
}

let currentEdit = {date: null, meal: null};
$('.edit-menu-btn').click(function() {
  const date = $(this).data('date');
  const meal = $(this).data('meal');
  currentEdit = {date, meal};
  renderRecipeSelector((window.menuData[date] && window.menuData[date][meal]) || []);
  $('#menuEditModal').modal('show');
});

$('#modalConfirmBtn').click(function() {
  const selected = getSelectedRecipes();
  if (!window.menuData[currentEdit.date]) window.menuData[currentEdit.date] = {};
  window.menuData[currentEdit.date][currentEdit.meal] = selected;
  $('#menuEditModal').modal('hide');
  renderTable();
});

$('#saveBtn').click(function() {
  // 禁用按钮，防止重复点击
  var $btn = $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 保存中...');
  var $draftBtn = $('#saveDraftBtn').prop('disabled', true);

  // 准备要发送的数据
  var weekStart = Object.keys(weekDates)[0];
  var menuData = window.menuData || {};

  // 检查数据是否为空
  var hasData = false;
  for (var date in menuData) {
    for (var meal in menuData[date]) {
      if (menuData[date][meal] && menuData[date][meal].length > 0) {
        hasData = true;
        break;
      }
    }
    if (hasData) break;
  }

  if (!hasData) {
    toastr.warning('没有菜单数据可保存');
    $btn.prop('disabled', false).html('保存并发布');
    $draftBtn.prop('disabled', false);
    return;
  }

  // 打印日志，帮助调试
  console.log('准备保存的数据:', {
    area_id: {{ area_id }},
    week_start: weekStart,
    menu_data: {days: menuData}
  });

  // 发送AJAX请求
  $.ajax({
    url: '/api/menu-plan/week/save',
    type: 'POST',
    contentType: 'application/json',
    data: JSON.stringify({
      area_id: {{ area_id }},
      week_start: weekStart,
      menu_data: menuData
    }),
    success: function(resp) {
      console.log('保存响应:', resp);
      if (resp.success) {
        toastr.success('保存成功');
      } else {
        toastr.error(resp.message || '保存失败');
      }
    },
    error: function(xhr, status, error) {
      console.error('保存请求失败:', status, error);
      toastr.error('保存请求失败: ' + (error || status));
    },
    complete: function() {
      // 恢复按钮状态
      $btn.prop('disabled', false).html('保存并发布');
      $draftBtn.prop('disabled', false);
    }
  });
});

$('#saveDraftBtn').click(function() {
  $('#saveBtn').click();
});

// 页面加载完成后执行
$(function() {
  // 确保menuData已正确初始化
  if (!window.menuData) {
    window.menuData = {};
    console.warn('menuData未初始化，已创建空对象');
  }

  // 渲染表格
  renderTable();

  // 打印初始数据，帮助调试
  console.log('初始menuData:', window.menuData);
  console.log('weekDates:', weekDates);
});
</script>
{% endblock %}