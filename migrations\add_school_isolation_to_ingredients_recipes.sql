-- 为食材和食谱模块添加学校级隔离字段
-- 执行时间：2025年5月29日

PRINT '开始为食材和食谱模块添加学校级隔离字段...';

-- 1. 为食材表添加学校级隔离字段
PRINT '检查食材表字段...';

-- 添加 area_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[ingredients]') AND name = 'area_id')
BEGIN
    ALTER TABLE ingredients ADD area_id INT NULL;
    PRINT '已为食材表添加 area_id 字段';
END
ELSE
BEGIN
    PRINT '食材表 area_id 字段已存在';
END

-- 添加 is_global 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[ingredients]') AND name = 'is_global')
BEGIN
    ALTER TABLE ingredients ADD is_global BIT NULL;
    PRINT '已为食材表添加 is_global 字段';

    -- 设置默认值
    UPDATE ingredients SET is_global = 0 WHERE is_global IS NULL;

    -- 修改为 NOT NULL
    ALTER TABLE ingredients ALTER COLUMN is_global BIT NOT NULL;
    PRINT '已设置食材表 is_global 字段默认值';
END
ELSE
BEGIN
    PRINT '食材表 is_global 字段已存在';
END

-- 2. 为食谱表添加学校级隔离字段
PRINT '检查食谱表字段...';

-- 添加 area_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[recipes]') AND name = 'area_id')
BEGIN
    ALTER TABLE recipes ADD area_id INT NULL;
    PRINT '已为食谱表添加 area_id 字段';
END
ELSE
BEGIN
    PRINT '食谱表 area_id 字段已存在';
END

-- 添加 is_global 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[recipes]') AND name = 'is_global')
BEGIN
    ALTER TABLE recipes ADD is_global BIT NULL;
    PRINT '已为食谱表添加 is_global 字段';

    -- 设置默认值
    UPDATE recipes SET is_global = 0 WHERE is_global IS NULL;

    -- 修改为 NOT NULL
    ALTER TABLE recipes ALTER COLUMN is_global BIT NOT NULL;
    PRINT '已设置食谱表 is_global 字段默认值';
END
ELSE
BEGIN
    PRINT '食谱表 is_global 字段已存在';
END

-- 3. 添加外键约束
PRINT '添加外键约束...';

-- 食材表外键约束
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_ingredients_area_id')
BEGIN
    ALTER TABLE ingredients ADD CONSTRAINT FK_ingredients_area_id FOREIGN KEY (area_id) REFERENCES administrative_areas(id);
    PRINT '已为食材表添加外键约束';
END
ELSE
BEGIN
    PRINT '食材表外键约束已存在';
END

-- 食谱表外键约束
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_recipes_area_id')
BEGIN
    ALTER TABLE recipes ADD CONSTRAINT FK_recipes_area_id FOREIGN KEY (area_id) REFERENCES administrative_areas(id);
    PRINT '已为食谱表添加外键约束';
END
ELSE
BEGIN
    PRINT '食谱表外键约束已存在';
END

-- 4. 更新现有数据 - 将现有食材和食谱标记为全局（系统预设）
PRINT '更新现有数据...';

-- 检查字段是否存在后再更新
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[ingredients]') AND name = 'is_global')
BEGIN
    UPDATE ingredients SET is_global = 1 WHERE area_id IS NULL;
    PRINT '已将现有食材标记为全局数据';
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[recipes]') AND name = 'is_global')
BEGIN
    UPDATE recipes SET is_global = 1 WHERE area_id IS NULL;
    PRINT '已将现有食谱标记为全局数据';
END

-- 5. 创建索引以提高查询性能
PRINT '创建索引...';

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ingredients_area_id_is_global')
BEGIN
    CREATE INDEX IX_ingredients_area_id_is_global ON ingredients (area_id, is_global);
    PRINT '已为食材表创建复合索引';
END
ELSE
BEGIN
    PRINT '食材表复合索引已存在';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_recipes_area_id_is_global')
BEGIN
    CREATE INDEX IX_recipes_area_id_is_global ON recipes (area_id, is_global);
    PRINT '已为食谱表创建复合索引';
END
ELSE
BEGIN
    PRINT '食谱表复合索引已存在';
END

-- 6. 添加注释说明
PRINT '添加字段注释...';

-- 检查并添加食材表注释
IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID('ingredients') AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('ingredients') AND name = 'area_id'))
BEGIN
    EXEC sp_addextendedproperty
        @name = N'MS_Description',
        @value = N'学校区域ID，用于数据隔离。NULL表示全局数据',
        @level0type = N'SCHEMA', @level0name = N'dbo',
        @level1type = N'TABLE', @level1name = N'ingredients',
        @level2type = N'COLUMN', @level2name = N'area_id';
    PRINT '已添加食材表 area_id 字段注释';
END

IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID('ingredients') AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('ingredients') AND name = 'is_global'))
BEGIN
    EXEC sp_addextendedproperty
        @name = N'MS_Description',
        @value = N'是否为全局食材（系统预设）。1=全局，0=学校专用',
        @level0type = N'SCHEMA', @level0name = N'dbo',
        @level1type = N'TABLE', @level1name = N'ingredients',
        @level2type = N'COLUMN', @level2name = N'is_global';
    PRINT '已添加食材表 is_global 字段注释';
END

-- 检查并添加食谱表注释
IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID('recipes') AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('recipes') AND name = 'area_id'))
BEGIN
    EXEC sp_addextendedproperty
        @name = N'MS_Description',
        @value = N'学校区域ID，用于数据隔离。NULL表示全局数据',
        @level0type = N'SCHEMA', @level0name = N'dbo',
        @level1type = N'TABLE', @level1name = N'recipes',
        @level2type = N'COLUMN', @level2name = N'area_id';
    PRINT '已添加食谱表 area_id 字段注释';
END

IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID('recipes') AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('recipes') AND name = 'is_global'))
BEGIN
    EXEC sp_addextendedproperty
        @name = N'MS_Description',
        @value = N'是否为全局食谱（系统预设）。1=全局，0=学校专用',
        @level0type = N'SCHEMA', @level0name = N'dbo',
        @level1type = N'TABLE', @level1name = N'recipes',
        @level2type = N'COLUMN', @level2name = N'is_global';
    PRINT '已添加食谱表 is_global 字段注释';
END

PRINT '学校级隔离字段添加完成！';

-- 7. 验证数据完整性
PRINT '验证数据完整性...';

-- 检查字段是否存在后再查询
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[ingredients]') AND name = 'is_global')
   AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[ingredients]') AND name = 'area_id')
   AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[recipes]') AND name = 'is_global')
   AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[recipes]') AND name = 'area_id')
BEGIN
    SELECT
        '食材统计' as 类型,
        COUNT(*) as 总数,
        SUM(CASE WHEN is_global = 1 THEN 1 ELSE 0 END) as 全局数量,
        SUM(CASE WHEN area_id IS NOT NULL THEN 1 ELSE 0 END) as 学校专用数量
    FROM ingredients

    UNION ALL

    SELECT
        '食谱统计' as 类型,
        COUNT(*) as 总数,
        SUM(CASE WHEN is_global = 1 THEN 1 ELSE 0 END) as 全局数量,
        SUM(CASE WHEN area_id IS NOT NULL THEN 1 ELSE 0 END) as 学校专用数量
    FROM recipes;

    PRINT '数据统计完成！';
END
ELSE
BEGIN
    PRINT '字段添加可能未完成，跳过数据统计';
END

PRINT '=== 学校级隔离迁移脚本执行完成 ===';
