"""
周菜单路由模块 V2
使用服务层重构的周菜单路由
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.models import WeeklyMenu, WeeklyMenuRecipe, Recipe, AdministrativeArea, RecipeCategory
from app.utils.decorators import check_permission, handle_weekly_menu_error
from app.services.weekly_menu_service import WeeklyMenuService
from app.services.menu_sync_service import MenuSyncService
from datetime import datetime, date, timedelta
import json

weekly_menu_v2_bp = Blueprint('weekly_menu_v2', __name__)

@weekly_menu_v2_bp.route('/weekly-menu-v2')
@login_required
@check_permission('weekly_menu', 'view')
def index():
    """周菜单列表页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['ITEMS_PER_PAGE']
    area_id = request.args.get('area_id', type=int)
    status = request.args.get('status', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    week = request.args.get('week', '')  # 周筛选参数

    # 构建查询
    query = WeeklyMenu.query.filter(WeeklyMenu.area_id.in_(area_ids))

    # 应用筛选条件
    if area_id:
        query = query.filter_by(area_id=area_id)
    if status:
        query = query.filter_by(status=status)
    if start_date:
        query = query.filter(WeeklyMenu.week_start >= datetime.strptime(start_date, '%Y-%m-%d').date())
    if end_date:
        query = query.filter(WeeklyMenu.week_start <= datetime.strptime(end_date, '%Y-%m-%d').date())
    if week:
        try:
            # 解析周参数，格式为 "2025-W20"（第20周）
            year, week_number = week.split('-W')
            year = int(year)
            week_number = int(week_number)

            # 计算该周的开始日期（周一）
            first_day = date(year, 1, 1)
            if first_day.weekday() > 0:  # 如果1月1日不是周一
                first_day = first_day - timedelta(days=first_day.weekday())
            target_date = first_day + timedelta(weeks=week_number-1)

            # 筛选该周的菜单
            query = query.filter(WeeklyMenu.week_start == target_date)
            current_app.logger.info(f'按周筛选: {week}, 对应日期: {target_date}')
        except Exception as e:
            current_app.logger.error(f'解析周参数失败: {week}, 错误: {str(e)}')

    # 排序并分页
    weekly_menus = query.order_by(WeeklyMenu.week_start.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # 获取所有区域，用于筛选
    areas = accessible_areas

    # 为每个菜单添加周显示信息
    for menu in weekly_menus.items:
        # 计算周数
        week_number = menu.week_start.isocalendar()[1]
        # 格式化显示
        menu.week_display = f"{menu.week_start.year}年第{week_number}周({menu.week_start.strftime('%m-%d')}至{menu.week_end.strftime('%m-%d')})"

    return render_template('weekly_menu/index_v2.html',
                          title='周菜单列表',
                          weekly_menus=weekly_menus,
                          areas=areas,
                          area_id=area_id,
                          status=status,
                          start_date=start_date,
                          end_date=end_date,
                          week=week)

@weekly_menu_v2_bp.route('/weekly-menu-v2/plan', methods=['GET', 'POST'])
@login_required
@check_permission('weekly_menu', 'edit')
@handle_weekly_menu_error
def plan():
    """周菜单计划页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()

    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    week_start_str = request.args.get('week_start')

    # 如果没有指定区域，使用当前用户的区域
    if not area_id and current_user.area_id:
        area_id = current_user.area_id

    # 如果用户没有区域，且没有指定区域，提示选择区域
    if not area_id:
        flash('请选择一个学校进行菜单计划', 'warning')
        return render_template('weekly_menu/select_area.html',
                              areas=accessible_areas,
                              title='选择学校')

    # 检查用户是否有权限访问该区域
    if not current_user.is_admin() and not current_user.has_role('学校管理员') and not current_user.can_access_area_by_id(area_id):
        flash('您没有权限访问该区域', 'danger')
        return redirect(url_for('weekly_menu_v2.index'))

    # 获取区域信息
    area = AdministrativeArea.query.get_or_404(area_id)

    # 如果没有指定周开始日期，默认为本周一
    if not week_start_str:
        today = date.today()
        week_start = today - timedelta(days=today.weekday())  # 本周一
        week_start_str = week_start.strftime('%Y-%m-%d')
    else:
        week_start = datetime.strptime(week_start_str, '%Y-%m-%d')
        if isinstance(week_start, datetime):
            week_start = week_start.date()

    # 计算周结束日期（7天，周一至周日）
    week_end = week_start + timedelta(days=6)

    # 获取现有菜单
    existing_menu = WeeklyMenuService.get_menu(area_id, week_start)

    # 获取当前日期
    today = date.today()

    # 计算本周一和下周一
    this_week_monday = today - timedelta(days=today.weekday())
    next_week_monday = this_week_monday + timedelta(days=7)
    last_week_monday = this_week_monday - timedelta(days=7)

    # 确保全部为 date 类型
    if isinstance(this_week_monday, datetime):
        this_week_monday = this_week_monday.date()
    if isinstance(next_week_monday, datetime):
        next_week_monday = next_week_monday.date()
    if isinstance(last_week_monday, datetime):
        last_week_monday = last_week_monday.date()

    # 检查本周和上周菜单是否已发布
    this_week_menu = WeeklyMenuService.get_menu(area_id, this_week_monday)
    last_week_menu = WeeklyMenuService.get_menu(area_id, last_week_monday)

    this_week_published = this_week_menu and this_week_menu.status == '已发布'
    last_week_published = last_week_menu and last_week_menu.status == '已发布'

    # 确定是否可编辑
    is_editable = True

    # 如果是上周或更早的菜单，不可编辑
    if week_start <= last_week_monday:
        is_editable = False

    # 如果是本周菜单且已发布，不可编辑
    if week_start == this_week_monday and this_week_published:
        is_editable = False

    # 如果当前菜单已发布，不可编辑
    if existing_menu and existing_menu.status == '已发布':
        is_editable = False

    # 生成周次导航数据
    available_weeks = []

    # 上周
    last_week_end = last_week_monday + timedelta(days=6)
    available_weeks.append({
        'start_date': last_week_monday.strftime('%Y-%m-%d'),
        'end_date': last_week_end.strftime('%Y-%m-%d'),
        'display_text': f'上周 ({last_week_monday.strftime("%m-%d")}至{last_week_end.strftime("%m-%d")})',
        'is_editable': False,  # 上周不可编辑
        'is_viewable': True,
        'status': last_week_menu.status if last_week_menu else '未创建'
    })

    # 本周
    this_week_end = this_week_monday + timedelta(days=6)
    available_weeks.append({
        'start_date': this_week_monday.strftime('%Y-%m-%d'),
        'end_date': this_week_end.strftime('%Y-%m-%d'),
        'display_text': f'本周 ({this_week_monday.strftime("%m-%d")}至{this_week_end.strftime("%m-%d")})',
        'is_editable': not this_week_published,  # 如果本周菜单未发布，则可编辑
        'is_viewable': True,
        'status': this_week_menu.status if this_week_menu else '未创建'
    })

    # 下周
    next_week_end = next_week_monday + timedelta(days=6)
    next_week_menu = WeeklyMenuService.get_menu(area_id, next_week_monday)

    available_weeks.append({
        'start_date': next_week_monday.strftime('%Y-%m-%d'),
        'end_date': next_week_end.strftime('%Y-%m-%d'),
        'display_text': f'下周 ({next_week_monday.strftime("%m-%d")}至{next_week_end.strftime("%m-%d")})',
        'is_editable': True,  # 下周始终可编辑
        'is_viewable': True,
        'status': next_week_menu.status if next_week_menu else '未创建'
    })

    # 如果是POST请求，处理表单提交
    if request.method == 'POST':
        # 检查是否有编辑权限
        if not is_editable:
            flash('当前周菜单不可编辑', 'danger')
            return redirect(url_for('weekly_menu_v2.plan', area_id=area_id, week_start=week_start_str))

        # 获取表单数据
        menu_data = json.loads(request.form.get('menu_data', '{}'))

        # 获取或创建周菜单（只为本周和下周创建，上周只查看不创建）
        if existing_menu:
            weekly_menu_id = existing_menu.id
        else:
            # 确保只为本周或下周创建新菜单
            if week_start >= this_week_monday:
                weekly_menu_id = WeeklyMenuService.create_menu(area_id, week_start, current_user.id)
            else:
                flash('上周菜单只能查看，不能创建', 'warning')
                return redirect(url_for('weekly_menu_v2.plan', area_id=area_id, week_start=week_start_str))

        # 保存菜单数据
        WeeklyMenuService.save_menu(weekly_menu_id, menu_data)

        flash('周菜单计划已保存', 'success')
        return redirect(url_for('weekly_menu_v2.view', id=weekly_menu_id))

    # 获取所有食谱，按分类组织 - 优化为只显示系统食谱和本校食谱
    user_area = current_user.get_current_area()

    # 构建查询 - 优先显示学校专用食谱，系统食谱作为备选
    recipes_query = Recipe.query.filter(
        Recipe.status == 1,
        db.or_(
            Recipe.area_id == user_area.id if user_area else None,  # 本校专用食谱
            Recipe.is_global == True,      # 系统食谱（可复制使用）
            Recipe.area_id.is_(None)       # 兼容旧数据（无area_id的食谱）
        )
    ).order_by(
        # 优先显示学校专用食谱
        db.case(
            (Recipe.area_id == (user_area.id if user_area else None), 1),
            (Recipe.is_global == True, 2),
            else_=3
        ),
        Recipe.priority.desc(),
        Recipe.id.desc()
    )

    recipes = recipes_query.all()
    recipes_by_category = {}
    for recipe in recipes:
        category = recipe.category
        if category not in recipes_by_category:
            recipes_by_category[category] = []
        recipes_by_category[category].append(recipe)

    # 生成周日期数据
    week_dates = {}
    weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    for i in range(7):  # 7天，周一到周日
        current_date = week_start + timedelta(days=i)
        date_str = current_date.strftime('%Y-%m-%d')
        week_dates[date_str] = {
            'date': date_str,
            'weekday': weekdays[i]
        }

    # 如果已存在菜单计划，获取现有数据
    menu_data = {}
    if existing_menu:
        menu_recipes = WeeklyMenuRecipe.query.filter_by(weekly_menu_id=existing_menu.id).all()

        for menu_recipe in menu_recipes:
            # 计算日期字符串
            day_date = week_start + timedelta(days=menu_recipe.day_of_week - 1)
            date_str = day_date.strftime('%Y-%m-%d')

            # 初始化日期和餐次
            if date_str not in menu_data:
                menu_data[date_str] = {}
            if menu_recipe.meal_type not in menu_data[date_str]:
                menu_data[date_str][menu_recipe.meal_type] = []

            # 添加食谱
            recipe_data = {
                'id': menu_recipe.recipe_id or f'custom_{menu_recipe.id}',
                'name': menu_recipe.recipe_name
            }
            menu_data[date_str][menu_recipe.meal_type].append(recipe_data)

    return render_template('weekly_menu/plan_v2.html',
                          title=f'{area.name}周菜单计划',
                          area=area,
                          area_id=area_id,
                          week_start=week_start_str,
                          week_end=week_end.strftime('%Y-%m-%d'),
                          week_dates=week_dates,
                          recipes_by_category=recipes_by_category,
                          menu_data=menu_data,
                          existing_menu=existing_menu,
                          is_editable=is_editable,
                          available_weeks=available_weeks,
                          this_week_monday=this_week_monday.strftime('%Y-%m-%d'))

@weekly_menu_v2_bp.route('/weekly-menu-v2/<int:id>')
@login_required
@check_permission('weekly_menu', 'view')
@handle_weekly_menu_error
def view(id):
    """查看周菜单计划详情"""
    # 获取菜单
    weekly_menu = WeeklyMenu.query.options(
        db.joinedload(WeeklyMenu.area),
        db.joinedload(WeeklyMenu.creator)
    ).get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.is_admin() and not current_user.has_role('学校管理员') and not current_user.can_access_area_by_id(weekly_menu.area_id):
        flash('您没有权限查看该周菜单计划', 'danger')
        return redirect(url_for('weekly_menu_v2.index'))

    # 获取菜单食谱
    menu_recipes = WeeklyMenuRecipe.query.filter_by(weekly_menu_id=id).all()

    # 按日期和餐次组织数据
    week_data = {}
    weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

    # 初始化数据结构
    for i in range((weekly_menu.week_end - weekly_menu.week_start).days + 1):
        current_date = weekly_menu.week_start + timedelta(days=i)
        date_str = current_date.strftime('%Y-%m-%d')
        day_of_week = current_date.weekday()  # 0-6

        week_data[date_str] = {
            'date': date_str,
            'weekday': weekdays[day_of_week],
            'meals': {
                '早餐': [],
                '午餐': [],
                '晚餐': []
            }
        }

    # 填充菜单数据
    for recipe in menu_recipes:
        day_date = weekly_menu.week_start + timedelta(days=recipe.day_of_week - 1)
        date_str = day_date.strftime('%Y-%m-%d')

        if date_str in week_data:
            week_data[date_str]['meals'][recipe.meal_type].append({
                'id': recipe.id,
                'recipe_id': recipe.recipe_id,
                'name': recipe.recipe_name
            })

    return render_template('weekly_menu/view_v2.html',
                          title=f'{weekly_menu.area.name}周菜单计划',
                          weekly_menu=weekly_menu,
                          week_data=week_data)

@weekly_menu_v2_bp.route('/weekly-menu-v2/<int:id>/publish', methods=['POST'])
@login_required
@check_permission('weekly_menu', 'publish')
@handle_weekly_menu_error
def publish(id):
    """发布周菜单计划"""
    # 获取菜单
    weekly_menu = WeeklyMenu.query.get_or_404(id)

    # 检查用户是否有权限发布
    if not current_user.is_admin() and not current_user.has_role('学校管理员') and not current_user.can_access_area_by_id(weekly_menu.area_id):
        flash('您没有权限发布该周菜单计划', 'danger')
        return redirect(url_for('weekly_menu_v2.index'))

    # 发布菜单
    WeeklyMenuService.publish_menu(id)

    # 同步周菜单数据到工作日志
    try:
        sync_result = MenuSyncService.sync_weekly_menu_to_daily_logs(id)
        if sync_result['success']:
            results = sync_result['results']
            flash(f"周菜单计划已发布，并已同步到工作日志（更新{results['updated_logs']}条，创建{results['created_logs']}条）", 'success')
        else:
            flash(f"周菜单计划已发布，但同步到工作日志失败: {sync_result['message']}", 'warning')
    except Exception as e:
        flash(f"周菜单计划已发布，但同步到工作日志时出错: {str(e)}", 'warning')

    # 获取周开始日期和区域ID，用于重定向回周菜单计划页面
    week_start = weekly_menu.week_start.strftime('%Y-%m-%d')
    area_id = weekly_menu.area_id

    # 重定向回周菜单计划页面
    return redirect(url_for('weekly_menu_v2.plan', area_id=area_id, week_start=week_start))

@weekly_menu_v2_bp.route('/weekly-menu-v2/<int:id>/unpublish', methods=['POST'])
@login_required
@check_permission('weekly_menu', 'publish')
@handle_weekly_menu_error
def unpublish(id):
    """解除发布周菜单计划"""
    # 获取菜单
    weekly_menu = WeeklyMenu.query.get_or_404(id)

    # 检查用户是否有权限解除发布
    if not current_user.is_admin() and not current_user.has_role('学校管理员') and not current_user.can_access_area_by_id(weekly_menu.area_id):
        flash('您没有权限解除发布该周菜单计划', 'danger')
        return redirect(url_for('weekly_menu_v2.index'))

    # 解除发布菜单
    WeeklyMenuService.unpublish_menu(id)

    flash('周菜单计划已解除发布，现在可以重新编辑', 'success')

    # 重定向回周菜单列表页面
    return redirect(url_for('weekly_menu_v2.index'))

@weekly_menu_v2_bp.route('/weekly-menu-v2/copy', methods=['GET', 'POST'])
@login_required
@check_permission('weekly_menu', 'edit')
@handle_weekly_menu_error
def copy():
    """复制周菜单"""
    if request.method == 'GET':
        # 获取可用的历史菜单
        area_id = request.args.get('area_id', type=int)
        if not area_id:
            area_id = current_user.area_id

        # 检查权限
        if not current_user.can_access_area_by_id(area_id):
            flash('您没有权限访问该区域的菜单', 'error')
            return redirect(url_for('weekly_menu_v2.index'))

        # 获取历史菜单
        historical_menus = WeeklyMenu.query.filter_by(area_id=area_id).order_by(WeeklyMenu.week_start.desc()).limit(10).all()

        # 获取可用的目标周次
        today = date.today()
        current_week_start = today - timedelta(days=today.weekday())
        next_week_start = current_week_start + timedelta(days=7)

        target_weeks = [
            {
                'start_date': current_week_start.strftime('%Y-%m-%d'),
                'end_date': (current_week_start + timedelta(days=6)).strftime('%Y-%m-%d'),
                'display_text': f'本周 ({current_week_start.strftime("%m-%d")}至{(current_week_start + timedelta(days=6)).strftime("%m-%d")})'
            },
            {
                'start_date': next_week_start.strftime('%Y-%m-%d'),
                'end_date': (next_week_start + timedelta(days=6)).strftime('%Y-%m-%d'),
                'display_text': f'下周 ({next_week_start.strftime("%m-%d")}至{(next_week_start + timedelta(days=6)).strftime("%m-%d")})'
            }
        ]

        return render_template(
            'weekly_menu/copy_v2.html',
            historical_menus=historical_menus,
            target_weeks=target_weeks,
            area_id=area_id
        )
    else:
        # 处理复制请求
        source_menu_id = request.form.get('source_menu_id', type=int)
        target_week_start = request.form.get('target_week_start')
        area_id = request.form.get('area_id', type=int)

        if not source_menu_id or not target_week_start or not area_id:
            flash('参数错误', 'error')
            return redirect(url_for('weekly_menu_v2.copy'))

        # 检查权限
        if not current_user.can_access_area_by_id(area_id):
            flash('您没有权限访问该区域的菜单', 'error')
            return redirect(url_for('weekly_menu_v2.index'))

        # 复制菜单
        new_menu_id = WeeklyMenuService.copy_menu(source_menu_id, target_week_start, area_id)

        flash('菜单复制成功', 'success')
        return redirect(url_for('weekly_menu_v2.plan', area_id=area_id, week_start=target_week_start))

@weekly_menu_v2_bp.route('/weekly-menu-v2/<int:id>/print')
@login_required
@check_permission('weekly_menu', 'view')
@handle_weekly_menu_error
def print_menu(id):
    """打印周菜单计划 - A4横向格式"""
    # 获取菜单
    weekly_menu = WeeklyMenu.query.options(
        db.joinedload(WeeklyMenu.area),
        db.joinedload(WeeklyMenu.creator)
    ).get_or_404(id)

    # 检查用户是否有权限打印
    if not current_user.is_admin() and not current_user.has_role('学校管理员') and not current_user.can_access_area_by_id(weekly_menu.area_id):
        flash('您没有权限打印该周菜单计划', 'danger')
        return redirect(url_for('weekly_menu_v2.index'))

    # 获取菜单食谱
    menu_recipes = WeeklyMenuRecipe.query.filter_by(weekly_menu_id=id).all()

    # 按日期和餐次组织数据
    week_data = {}
    weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

    # 初始化数据结构
    for i in range((weekly_menu.week_end - weekly_menu.week_start).days + 1):
        current_date = weekly_menu.week_start + timedelta(days=i)
        date_str = current_date.strftime('%Y-%m-%d')
        day_of_week = current_date.weekday()  # 0-6

        week_data[date_str] = {
            'date': date_str,
            'weekday': weekdays[day_of_week],
            'meals': {
                '早餐': [],
                '午餐': [],
                '晚餐': []
            }
        }

    # 填充菜单数据
    for recipe in menu_recipes:
        day_date = weekly_menu.week_start + timedelta(days=recipe.day_of_week - 1)
        date_str = day_date.strftime('%Y-%m-%d')

        if date_str in week_data:
            week_data[date_str]['meals'][recipe.meal_type].append({
                'id': recipe.id,
                'recipe_id': recipe.recipe_id,
                'name': recipe.recipe_name
            })

    return render_template('weekly_menu/print_v2.html',
                          title=f'{weekly_menu.area.name}周菜单计划',
                          weekly_menu=weekly_menu,
                          week_data=week_data)
