{% extends 'base.html' %}

{% block title %}周菜单计划{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/jquery-ui/css/jquery-ui.min.css') }}">
<style nonce="{{ csp_nonce }}">
    .week-planner {
        margin-top: 20px;
    }
    .day-column {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 15px;
        background-color: #f9f9f9;
    }
    .day-header {
        background-color: #4e73df;
        color: white;
        padding: 8px;
        border-radius: 5px;
        margin-bottom: 10px;
        text-align: center;
    }
    .meal-slot {
        border: 1px dashed #ccc;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 10px;
        min-height: 100px;
        background-color: white;
    }
    .meal-header {
        font-weight: bold;
        margin-bottom: 5px;
        padding-bottom: 5px;
        border-bottom: 1px solid #eee;
    }
    .meal-content {
        min-height: 60px;
    }
    .meal-item {
        background-color: #e8f4ff;
        border: 1px solid #b8daff;
        border-radius: 5px;
        padding: 8px;
        margin-bottom: 5px;
        cursor: move;
    }
    .recipe-list {
        margin-top: 5px;
        font-size: 0.9em;
    }
    .recipe-item {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 3px;
        padding: 3px 6px;
        margin: 2px;
        display: inline-block;
    }
    .recipe-selector {
        max-height: 400px;
        overflow-y: auto;
    }
    .recipe-category {
        margin-bottom: 15px;
    }
    .recipe-category-title {
        font-weight: bold;
        margin-bottom: 5px;
    }
    .recipe-option {
        cursor: pointer;
        padding: 5px;
        border: 1px solid #ddd;
        border-radius: 3px;
        margin-bottom: 5px;
        background-color: white;
    }
    .recipe-option:hover {
        background-color: #f0f0f0;
    }
    .recipe-option.selected {
        background-color: #d4edda;
        border-color: #c3e6cb;
    }
    .draggable {
        cursor: move;
    }
    .drop-hover {
        background-color: rgba(0, 123, 255, 0.1);
        border: 2px dashed #007bff;
    }
    .ui-draggable-dragging {
        z-index: 1000;
        box-shadow: 0 0 10px rgba(0,0,0,0.2);
    }
    .sync-status {
        position: fixed;
        bottom: 20px;
        right: 20px;
        padding: 10px;
        border-radius: 5px;
        display: none;
    }
    .sync-status.success {
        background-color: #d4edda;
        color: #155724;
    }
    .sync-status.error {
        background-color: #f8d7da;
        color: #721c24;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">筛选条件</h6>
                </div>
                <div class="card-body">
                    <form method="get" action="{{ url_for('menu_plan.week') }}" class="form-inline">
                        <div class="form-group mr-3">
                            <label for="area_id" class="mr-2">区域:</label>
                            <select name="area_id" id="area_id" class="form-control">
                                {% for area in areas %}
                                <option value="{{ area.id }}" {% if area.id == area_id %}selected{% endif %}>{{ area.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group mr-3">
                            <label for="week_start" class="mr-2">周开始日期:</label>
                            <input type="date" name="week_start" id="week_start" class="form-control" value="{{ week_start_str }}">
                        </div>
                        <button type="submit" class="btn btn-primary">查询</button>
                        <button type="button" id="save-all-btn" class="btn btn-success ml-3">保存所有菜单</button>
                        <button type="button" id="sync-btn" class="btn btn-info ml-3">同步数据</button>
                    </form>
                </div>
            </div>

            <!-- 周菜单规划 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">周菜单规划</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 食谱选择器 -->
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="m-0 font-weight-bold text-primary">食谱选择</h6>
                                </div>
                                <div class="card-body recipe-selector">
                                    {% for category, recipes in recipes_by_category.items() %}
                                    <div class="recipe-category">
                                        <div class="recipe-category-title">{{ category }}</div>
                                        {% for recipe in recipes %}
                                        <div class="recipe-option" data-recipe-id="{{ recipe.id }}" data-recipe-name="{{ recipe.name }}">
                                            {{ recipe.name }}
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>

                        <!-- 周菜单表格 -->
                        <div class="col-md-9">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th style="width: 12%">日期</th>
                                            <th style="width: 29%">早餐</th>
                                            <th style="width: 29%">午餐</th>
                                            <th style="width: 29%">晚餐</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for date_str, day_data in menu_data.days.items() %}
                                        <tr>
                                            <td>
                                                <div class="week-date">{{ day_data.weekday }}</div>
                                                <div>{{ date_str[5:] }}</div>
                                            </td>
                                            {% for meal_type in ['早餐', '午餐', '晚餐'] %}
                                            <td class="meal-cell" data-date="{{ date_str }}" data-meal-type="{{ meal_type }}">
                                                <div class="meal-header">{{ meal_type }}</div>
                                                <div class="meal-content">
                                                    {% if day_data.meals[meal_type] %}
                                                    {% for recipe in day_data.meals[meal_type].recipes %}
                                                    <div class="meal-item" data-recipe-id="{{ recipe.id }}">
                                                        {{ recipe.name }}
                                                        <span class="badge badge-info">{{ recipe.quantity }}{{ recipe.unit }}</span>
                                                        <button type="button" class="btn btn-sm btn-danger float-right remove-recipe">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                    {% endfor %}
                                                    {% endif %}
                                                </div>
                                            </td>
                                            {% endfor %}
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 同步状态提示 -->
<div id="sync-status" class="sync-status"></div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery-ui/js/jquery-ui.min.js') }}"></script>
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 初始化IndexedDB
    const dbName = 'MenuDB';
    const dbVersion = 1;
    let db;

    const request = indexedDB.open(dbName, dbVersion);

    request.onerror = function(event) {
        console.error('数据库打开失败:', event.target.error);
    };

    request.onsuccess = function(event) {
        db = event.target.result;
        console.log('数据库打开成功');
        // 加载本地数据
        loadLocalData();
    };

    request.onupgradeneeded = function(event) {
        const db = event.target.result;
        if (!db.objectStoreNames.contains('menus')) {
            db.createObjectStore('menus', { keyPath: 'id' });
        }
    };

    // 保存数据到IndexedDB
    function saveToIndexedDB(data) {
        return new Promise((resolve, reject) => {
            const transaction = db.transaction(['menus'], 'readwrite');
            const store = transaction.objectStore('menus');
            const request = store.put({
                id: 'weekMenu',
                data: data,
                timestamp: new Date().getTime()
            });

            request.onsuccess = function() {
                resolve();
            };

            request.onerror = function(event) {
                reject(event.target.error);
            };
        });
    }

    // 从IndexedDB加载数据
    function loadFromIndexedDB() {
        return new Promise((resolve, reject) => {
            const transaction = db.transaction(['menus'], 'readonly');
            const store = transaction.objectStore('menus');
            const request = store.get('weekMenu');

            request.onsuccess = function(event) {
                resolve(event.target.result);
            };

            request.onerror = function(event) {
                reject(event.target.error);
            };
        });
    }

    // 加载本地数据
    async function loadLocalData() {
        try {
            const localData = await loadFromIndexedDB();
            if (localData && localData.data) {
                // 检查数据是否过期（24小时）
                const now = new Date().getTime();
                if (now - localData.timestamp < 24 * 60 * 60 * 1000) {
                    window.menuData = localData.data;
                    updateUI();
                }
            }
        } catch (error) {
            console.error('加载本地数据失败:', error);
        }
    }

    // 更新UI显示
    function updateUI() {
        $('.meal-content').empty();
        Object.entries(window.menuData.days).forEach(([date, dayData]) => {
            Object.entries(dayData.meals).forEach(([mealType, recipes]) => {
                const cell = $(`.meal-cell[data-date="${date}"][data-meal-type="${mealType}"] .meal-content`);
                recipes.forEach(recipe => {
                    const mealItem = $('<div class="meal-item" data-recipe-id="' + recipe.id + '">' +
                        recipe.name +
                        '<span class="badge badge-info">' + recipe.quantity + recipe.unit + '</span>' +
                        '<button type="button" class="btn btn-sm btn-danger float-right remove-recipe">' +
                        '<i class="fas fa-times"></i>' +
                        '</button>' +
                        '</div>');
                    cell.append(mealItem);
                });
            });
        });
    }

    // 初始化拖曳功能
    $('.meal-content').sortable({
        connectWith: '.meal-content',
        placeholder: 'drop-hover',
        handle: '.meal-item',
        update: function(event, ui) {
            updateMenuData();
        }
    });

    // 食谱选择
    $('.recipe-option').click(function() {
        var recipeId = $(this).data('recipe-id');
        var recipeName = $(this).data('recipe-name');
        
        // 创建新的菜品项
        var mealItem = $('<div class="meal-item" data-recipe-id="' + recipeId + '">' +
            recipeName +
            '<span class="badge badge-info">1份</span>' +
            '<button type="button" class="btn btn-sm btn-danger float-right remove-recipe">' +
            '<i class="fas fa-times"></i>' +
            '</button>' +
            '</div>');

        // 添加到当前选中的餐次
        $('.meal-cell.drop-hover .meal-content').append(mealItem);
        
        // 更新菜单数据
        updateMenuData();
    });

    // 删除菜品
    $(document).on('click', '.remove-recipe', function() {
        $(this).closest('.meal-item').remove();
        updateMenuData();
    });

    // 保存所有菜单
    $('#save-all-btn').click(function() {
        var menuData = {
            area_id: $('#area_id').val(),
            week_start: $('#week_start').val(),
            menu_data: window.menuData
        };

        $.ajax({
            url: '/api/menu-plan/week/save',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(menuData),
            success: function(response) {
                if (response.success) {
                    showSyncStatus('保存成功', 'success');
                    // 保存到本地存储
                    saveToIndexedDB(window.menuData);
                } else {
                    showSyncStatus('保存失败: ' + response.message, 'error');
                }
            },
            error: function() {
                showSyncStatus('保存失败，请重试', 'error');
            }
        });
    });

    // 同步数据
    $('#sync-btn').click(function() {
        var menuData = {
            area_id: $('#area_id').val(),
            week_start: $('#week_start').val()
        };

        $.ajax({
            url: '/api/menu-plan/week/get',
            type: 'GET',
            data: menuData,
            success: function(response) {
                if (response.success) {
                    window.menuData = response.data;
                    updateUI();
                    // 保存到本地存储
                    saveToIndexedDB(window.menuData);
                    showSyncStatus('同步成功', 'success');
                } else {
                    showSyncStatus('同步失败: ' + response.message, 'error');
                }
            },
            error: function() {
                showSyncStatus('同步失败，请重试', 'error');
            }
        });
    });

    // 显示同步状态
    function showSyncStatus(message, type) {
        const status = $('#sync-status');
        status.removeClass('success error').addClass(type);
        status.text(message).fadeIn();
        setTimeout(() => status.fadeOut(), 3000);
    }

    // 更新菜单数据
    function updateMenuData() {
        var menuData = {
            area_id: $('#area_id').val(),
            week_start: $('#week_start').val(),
            days: {}
        };

        $('.meal-cell').each(function() {
            var date = $(this).data('date');
            var mealType = $(this).data('meal-type');
            var recipes = [];

            $(this).find('.meal-item').each(function() {
                var recipeId = $(this).data('recipe-id');
                var recipeName = $(this).text().trim();
                var quantity = $(this).find('.badge').text().match(/\d+/)[0];
                var unit = $(this).find('.badge').text().match(/[^\d]+/)[0];

                recipes.push({
                    id: recipeId,
                    name: recipeName,
                    quantity: parseInt(quantity),
                    unit: unit
                });
            });

            if (!menuData.days[date]) {
                menuData.days[date] = {
                    date: date,
                    weekday: $('.meal-cell[data-date="' + date + '"]').closest('tr').find('.week-date').text(),
                    meals: {
                        '早餐': [],
                        '午餐': [],
                        '晚餐': []
                    }
                };
            }

            menuData.days[date].meals[mealType] = recipes;
        });

        // 保存到全局变量
        window.menuData = menuData;

        // 保存到本地存储
        saveToIndexedDB(menuData);
    }

    // 初始化菜单数据
    window.menuData = {{ menu_data|tojson|safe }};
});
</script>
{% endblock %} 