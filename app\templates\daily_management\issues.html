{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .badge-pending {
        background-color: #f6c23e;
        color: #fff;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .badge-fixing {
        background-color: #4e73df;
        color: #fff;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .badge-fixed {
        background-color: #1cc88a;
        color: #fff;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .issue-type-badge {
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .issue-food {
        background-color: #e74a3b;
        color: white;
    }

    .issue-equipment {
        background-color: #4e73df;
        color: white;
    }

    .issue-hygiene {
        background-color: #1cc88a;
        color: white;
    }

    .issue-other {
        background-color: #f6c23e;
        color: white;
    }

    .action-buttons .btn {
        margin-right: 5px;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .description-cell {
        max-width: 300px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 导入导航宏 -->
    {% from 'daily_management/components/navigation.html' import daily_management_header %}

    <!-- 显示导航和学校信息 -->
    {{ daily_management_header(title, school, log, 'issues') }}

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">问题记录列表</h6>
            <div>
                <a href="{{ url_for('daily_management.add_issue', log_id=log.id) }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus mr-1"></i> 添加问题记录
                </a>
                <a href="{{ url_for('daily_management.print_issues', log_id=log.id) }}" class="btn btn-success btn-sm">
                    <i class="fas fa-print mr-1"></i> 打印问题记录
                </a>
                <a href="{{ url_for('daily_management.edit_log', date_str=log.log_date|format_datetime('%Y-%m-%d')) }}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-left mr-1"></i> 返回日志
                </a>
            </div>
        </div>
        <div class="card-body">
            {% if issues %}
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>问题类型</th>
                            <th>发现时间</th>
                            <th>描述</th>
                            <th>责任人</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for issue in issues %}
                        <tr>
                            <td>
                                {% if '食品' in issue.issue_type %}
                                <span class="issue-type-badge issue-food">{{ issue.issue_type }}</span>
                                {% elif '设备' in issue.issue_type %}
                                <span class="issue-type-badge issue-equipment">{{ issue.issue_type }}</span>
                                {% elif '卫生' in issue.issue_type %}
                                <span class="issue-type-badge issue-hygiene">{{ issue.issue_type }}</span>
                                {% else %}
                                <span class="issue-type-badge issue-other">{{ issue.issue_type }}</span>
                                {% endif %}
                            </td>
                            <td>{{ issue.found_time|format_datetime }}</td>
                            <td class="description-cell">{{ issue.description|truncate(50) }}</td>
                            <td>{{ issue.responsible_person or '-' }}</td>
                            <td>
                                {% if issue.status == 'pending' %}
                                <span class="badge badge-pending">待处理</span>
                                {% elif issue.status == 'fixing' %}
                                <span class="badge badge-fixing">处理中</span>
                                {% elif issue.status == 'fixed' %}
                                <span class="badge badge-fixed">已修复</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('daily_management.view_issue', issue_id=issue.id) }}" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('daily_management.edit_issue', issue_id=issue.id) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-danger btn-sm" data-toggle="modal" data-target="#deleteModal{{ issue.id }}">
                                    <i class="fas fa-trash"></i>
                                </button>

                                <!-- 删除确认模态框 -->
                                <div class="modal fade" id="deleteModal{{ issue.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel{{ issue.id }}" aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ issue.id }}">确认删除</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                确定要删除问题记录"{{ issue.issue_type }}"吗？此操作不可恢复。
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                                                <form action="{{ url_for('daily_management.delete_issue', issue_id=issue.id) }}" method="post">
                                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                    <button type="submit" class="btn btn-danger">确认删除</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-4">
                <p class="text-muted">暂无问题记录</p>
                <a href="{{ url_for('daily_management.add_issue', log_id=log.id) }}" class="btn btn-primary">
                    <i class="fas fa-plus mr-1"></i> 添加问题记录
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
