{% extends 'base.html' %}

{% block title %}入库检查管理{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .card {
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: all 0.3s;
        height: 100%;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .card-header {
        background: linear-gradient(to right, #f8f9fc, #ffffff);
        border-bottom: 1px solid #e3e6f0;
        border-radius: 8px 8px 0 0;
        padding: 1rem 1.25rem;
    }

    .card-body {
        padding: 1.25rem;
    }

    .status-badge {
        padding: 0.4em 0.8em;
        font-weight: 500;
        border-radius: 20px;
    }

    .status-normal {
        background-color: #e6f7ee;
        color: #1f9d55;
        border: 1px solid #c3e6cb;
    }

    .status-abnormal {
        background-color: #fde8e8;
        color: #e53e3e;
        border: 1px solid #f5c6cb;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeeba;
    }

    .btn {
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
        font-weight: 500;
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        transition: all 0.2s;
    }

    .btn:hover {
        transform: translateY(-1px);
    }

    .btn i {
        font-size: 0.875rem;
    }

    .table th {
        background: linear-gradient(to right, #f8f9fc, #ffffff);
        font-weight: 600;
        padding: 0.75rem 1rem;
        border-bottom: 2px solid #e3e6f0;
    }

    .table td {
        vertical-align: middle;
        padding: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">入库检查管理</h1>

    <!-- 入库检查流程引导 -->
    <div class="card mb-4 border-primary">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-clipboard-check"></i> 入库检查 - 流程指引</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle" style="font-size: 1.2rem; margin-right: 10px;"></i> <strong>提示：</strong> 入库检查是确保食材质量安全的重要环节，请仔细检查每批食材的质量状况、包装完整性和保质期等信息。
            </div>

            <div class="d-flex justify-content-between mt-3" style="margin-top: 10px;">
                <div style="flex: 1; padding: 0 10px;">
                    <small class="text-muted">上一步</small>
                    <p><i class="fas fa-shopping-cart"></i> 采购计划</p>
                    <small>已完成食材采购</small>
                    <div class="mt-2">
                        <a href="{{ url_for('purchase_order.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回采购订单
                        </a>
                    </div>
                </div>
                <div style="flex: 1; padding: 0 10px; border-left: 1px solid #dee2e6; border-right: 1px solid #dee2e6;" class="bg-light p-2 border rounded">
                    <small class="text-muted">当前步骤</small>
                    <p class="font-weight-bold"><i class="fas fa-clipboard-check"></i> 入库检查</p>
                    <small>检查采购食材质量</small>
                </div>
                <div style="flex: 1; padding: 0 10px;">
                    <small class="text-muted">下一步</small>
                    <p><i class="fas fa-dolly"></i> 食材入库</p>
                    <small>将合格食材入库</small>
                    <div class="mt-2">
                        <a href="{{ url_for('stock_in.index') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-arrow-right"></i> 前往入库管理
                        </a>
                    </div>
                </div>
            </div>

            <!-- 操作提示 -->
            <div class="alert alert-light border mt-3">
                <h6 class="alert-heading"><i class="fas fa-lightbulb text-warning"></i> 操作提示</h6>
                <ul class="mb-0">
                    <li>点击"开始检查"按钮，对待检查的采购订单进行检查</li>
                    <li>检查食材的外观、气味、包装完整性等</li>
                    <li>记录检查结果，如有异常情况请详细说明</li>
                    <li>检查通过的食材将自动生成入库单</li>
                    <li>检查不通过的食材需要联系供应商处理</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 待检查采购订单 -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clipboard-check mr-1"></i> 待检查采购订单
                    </h6>
                </div>
                <div class="card-body">
                    {% if pending_orders %}
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>订单编号</th>
                                    <th>供应商</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in pending_orders %}
                                <tr>
                                    <td>{{ order.id }}</td>
                                    <td>{{ order.supplier.name if order.supplier else '未知' }}</td>
                                    <td>
                                        <span class="status-badge status-pending">{{ order.status }}</span>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('inspection.create', purchase_order_id=order.id) }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-clipboard-check mr-1"></i> 开始检查
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-3x text-gray-300 mb-3"></i>
                        <p class="text-muted">暂无待检查采购订单</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 最近检查记录 -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history mr-1"></i> 最近检查记录
                    </h6>
                </div>
                <div class="card-body">
                    {% if recent_inspections %}
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>检查项目</th>
                                    <th>状态</th>
                                    <th>检查时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for inspection in recent_inspections %}
                                <tr>
                                    <td>{{ inspection.inspection_item }}</td>
                                    <td>
                                        {% if inspection.status == 'normal' %}
                                        <span class="status-badge status-normal">正常</span>
                                        {% elif inspection.status == 'abnormal' %}
                                        <span class="status-badge status-abnormal">异常</span>
                                        {% else %}
                                        <span class="status-badge status-pending">待检查</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ inspection.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        <a href="{{ url_for('inspection.view', id=inspection.id) }}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye mr-1"></i> 查看
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-3x text-gray-300 mb-3"></i>
                        <p class="text-muted">暂无检查记录</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
