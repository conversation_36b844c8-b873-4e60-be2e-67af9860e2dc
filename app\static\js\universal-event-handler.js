/**
 * 通用事件处理器
 * 处理从内联事件迁移过来的事件处理
 */

(function() {
    'use strict';
    
    // 等待 DOM 加载完成
    document.addEventListener('DOMContentLoaded', function() {
        
        // 处理打印按钮
        document.querySelectorAll('.print-button').forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                window.print();
            });
        });
        
        // 处理返回按钮
        document.querySelectorAll('.back-button').forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                history.back();
            });
        });
        
        // 处理刷新按钮
        document.querySelectorAll('.reload-button').forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                location.reload();
            });
        });
        
        // 处理通用 onclick 事件
        document.querySelectorAll('[data-onclick]').forEach(function(element) {
            const funcCall = element.getAttribute('data-onclick');
            element.addEventListener('click', function(e) {
                e.preventDefault();
                try {
                    // 安全地执行函数调用
                    if (typeof safeEval === 'function') {
                        safeEval(funcCall);
                    } else {
                        // 备用方案：使用 Function 构造器
                        new Function(funcCall)();
                    }
                } catch (error) {
                    console.error('事件处理器执行失败:', error, '函数:', funcCall);
                }
            });
        });
        
        // 处理表单提交事件
        document.querySelectorAll('[data-onsubmit]').forEach(function(form) {
            const funcCall = form.getAttribute('data-onsubmit');
            form.addEventListener('submit', function(e) {
                try {
                    if (typeof safeEval === 'function') {
                        const result = safeEval(funcCall);
                        if (result === false) {
                            e.preventDefault();
                        }
                    } else {
                        const result = new Function('return ' + funcCall)();
                        if (result === false) {
                            e.preventDefault();
                        }
                    }
                } catch (error) {
                    console.error('表单提交处理器执行失败:', error);
                    e.preventDefault();
                }
            });
        });
        
        // 处理 onchange 事件
        document.querySelectorAll('[data-onchange]').forEach(function(element) {
            const funcCall = element.getAttribute('data-onchange');
            element.addEventListener('change', function(e) {
                try {
                    if (typeof safeEval === 'function') {
                        safeEval(funcCall);
                    } else {
                        new Function(funcCall)();
                    }
                } catch (error) {
                    console.error('Change 事件处理器执行失败:', error);
                }
            });
        });
        
        console.log('✅ 通用事件处理器已初始化');
    });
    
    // 为动态添加的元素提供事件绑定
    window.bindUniversalEvents = function(container) {
        if (!container) container = document;
        
        // 重新绑定所有事件...
        // (这里可以复制上面的逻辑)
    };
    
})();