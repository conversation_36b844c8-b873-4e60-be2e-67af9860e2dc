{% extends "base.html" %}

{% block title %}{% if carousel %}编辑轮播图{% else %}添加轮播图{% endif %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-{% if carousel %}edit{% else %}plus{% endif %} me-2"></i>
                        {% if carousel %}编辑轮播图{% else %}添加轮播图{% endif %}
                    </h3>
                </div>

                <form method="POST" enctype="multipart/form-data" id="carouselForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <!-- 标题 -->
                                <div class="mb-3">
                                    <label for="title" class="form-label">
                                        图片标题 <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="title"
                                           name="title"
                                           value="{{ carousel.title if carousel else '' }}"
                                           maxlength="100"
                                           required>
                                    <div class="form-text">建议长度：10-50个字符</div>
                                </div>

                                <!-- 描述 -->
                                <div class="mb-3">
                                    <label for="description" class="form-label">图片描述</label>
                                    <textarea class="form-control"
                                              id="description"
                                              name="description"
                                              rows="3"
                                              maxlength="500">{{ carousel.description if carousel else '' }}</textarea>
                                    <div class="form-text">可选，用于SEO和无障碍访问</div>
                                </div>

                                <!-- 链接地址 -->
                                <div class="mb-3">
                                    <label for="link_url" class="form-label">点击链接</label>
                                    <input type="text"
                                           class="form-control"
                                           id="link_url"
                                           name="link_url"
                                           value="{{ carousel.link_url if carousel else '/' }}"
                                           placeholder="/">
                                    <div class="form-text">
                                        点击图片时跳转的链接<br>
                                        <small class="text-muted">默认跳转到首页 (/)，可修改为其他链接</small>
                                    </div>
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="setDefaultLink('/')">
                                            <i class="fas fa-home me-1"></i>设为首页
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setDefaultLink('/auth/login')">
                                            <i class="fas fa-sign-in-alt me-1"></i>设为登录
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setDefaultLink('/auth/register')">
                                            <i class="fas fa-user-plus me-1"></i>设为注册
                                        </button>
                                    </div>
                                </div>

                                <!-- 状态 -->
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input"
                                               type="checkbox"
                                               id="is_active"
                                               name="is_active"
                                               value="1"
                                               {% if not carousel or carousel.is_active %}checked{% endif %}>
                                        <label class="form-check-label" for="is_active">
                                            启用此轮播图
                                        </label>
                                    </div>
                                    <div class="form-text">禁用的轮播图不会在首页显示</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- 图片上传 -->
                                <div class="mb-3">
                                    <label for="images" class="form-label">
                                        轮播图片 {% if not carousel %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    <input type="file"
                                           class="form-control"
                                           id="images"
                                           name="images"
                                           accept="image/*"
                                           multiple
                                           {% if not carousel %}required{% endif %}>
                                    <div class="form-text">
                                        支持格式：PNG、JPG、JPEG、GIF、WebP<br>
                                        建议尺寸：1200x600像素<br>
                                        文件大小：每张不超过5MB<br>
                                        <strong>可以选择多张图片一次性上传</strong>
                                    </div>
                                </div>

                                <!-- 当前图片预览 -->
                                {% if carousel and carousel.image_path %}
                                <div class="mb-3">
                                    <label class="form-label">当前图片</label>
                                    <div class="border rounded p-2">
                                        <img src="{{ carousel.image_path }}"
                                             alt="{{ carousel.title }}"
                                             class="img-fluid rounded"
                                             id="currentImage">
                                    </div>
                                </div>
                                {% endif %}

                                <!-- 多图片预览 -->
                                <div class="mb-3" id="previewContainer" style="display: none;">
                                    <label class="form-label">图片预览</label>
                                    <div id="previewList" class="border rounded p-2">
                                        <!-- 动态生成预览图片 -->
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle"></i>
                                            选择多张图片时，每张都会创建为独立的轮播图
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('homepage_carousel.admin_list') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>返回列表
                            </a>
                            <div>
                                <button type="button" class="btn btn-outline-secondary me-2" data-onclick="resetForm()" data-event-id="60bcf14c">
                                    <i class="fas fa-undo me-1"></i>重置
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    {% if carousel %}更新轮播图{% else %}创建轮播图{% endif %}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
// 页面加载时确保默认值
document.addEventListener('DOMContentLoaded', function() {
    const linkInput = document.getElementById('link_url');

    // 如果是新建页面且输入框为空，设置默认值
    {% if not carousel %}
    if (!linkInput.value || linkInput.value.trim() === '') {
        linkInput.value = '/';
    }
    {% endif %}

    // 高亮当前链接对应的按钮
    highlightActiveButton(linkInput.value);
});

// 设置默认链接
function setDefaultLink(url) {
    const input = document.getElementById('link_url');
    input.value = url;

    // 高亮当前选中的按钮
    highlightActiveButton(url);

    // 添加视觉反馈
    input.focus();
    input.select();
}

// 高亮活跃按钮
function highlightActiveButton(url) {
    // 移除所有按钮的高亮
    document.querySelectorAll('.btn-outline-primary, .btn-outline-secondary').forEach(btn => {
        if (btn.onclick && btn.onclick.toString().includes('setDefaultLink')) {
            btn.className = btn.className.replace('btn-outline-primary', 'btn-outline-secondary');
        }
    });

    // 高亮匹配的按钮
    document.querySelectorAll('button[onclick*="setDefaultLink"]').forEach(btn => {
        const btnUrl = btn.onclick.toString().match(/setDefaultLink\('([^']+)'\)/);
        if (btnUrl && btnUrl[1] === url) {
            btn.className = btn.className.replace('btn-outline-secondary', 'btn-outline-primary');
        }
    });
}

// 多图片预览功能
document.getElementById('images').addEventListener('change', function(e) {
    const files = e.target.files;
    const previewContainer = document.getElementById('previewContainer');
    const previewList = document.getElementById('previewList');

    // 清空之前的预览
    previewList.innerHTML = '';

    if (files.length > 0) {
        let validFiles = 0;
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
        const maxSize = 5 * 1024 * 1024; // 5MB

        Array.from(files).forEach((file, index) => {
            // 检查文件类型
            if (!allowedTypes.includes(file.type)) {
                alert(`文件 "${file.name}" 不支持，请选择 PNG、JPG、JPEG、GIF 或 WebP 格式的图片`);
                return;
            }

            // 检查文件大小
            if (file.size > maxSize) {
                alert(`文件 "${file.name}" 大小超过5MB限制`);
                return;
            }

            validFiles++;

            // 创建预览容器
            const previewItem = document.createElement('div');
            previewItem.className = 'mb-3 p-2 border rounded bg-light';

            // 创建图片预览
            const reader = new FileReader();
            reader.onload = function(e) {
                previewItem.innerHTML = `
                    <div class="row align-items-center">
                        <div class="col-4">
                            <img src="${e.target.result}" class="img-fluid rounded" alt="预览 ${index + 1}" style="max-height: 100px;">
                        </div>
                        <div class="col-8">
                            <h6 class="mb-1">图片 ${index + 1}</h6>
                            <p class="mb-1 text-muted small">${file.name}</p>
                            <p class="mb-0 text-muted small">大小: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                            <div class="mt-2">
                                <input type="text" class="form-control form-control-sm"
                                       placeholder="图片标题（可选）"
                                       name="image_titles[]">
                            </div>
                        </div>
                    </div>
                `;
                previewList.appendChild(previewItem);
            };
            reader.readAsDataURL(file);
        });

        if (validFiles > 0) {
            previewContainer.style.display = 'block';

            // 显示提示信息
            const infoDiv = document.createElement('div');
            infoDiv.className = 'alert alert-info mt-2';
            infoDiv.innerHTML = `
                <i class="fas fa-info-circle"></i>
                已选择 ${validFiles} 张图片，将创建 ${validFiles} 个轮播图项目
            `;
            previewList.appendChild(infoDiv);
        } else {
            previewContainer.style.display = 'none';
        }
    } else {
        previewContainer.style.display = 'none';
    }
});

// 设置默认链接
function setDefaultLink(url) {
    document.getElementById('link_url').value = url;
}

// 重置表单
function resetForm() {
    if (confirm('确定要重置表单吗？所有未保存的更改将丢失。')) {
        document.getElementById('carouselForm').reset();
        document.getElementById('previewContainer').style.display = 'none';

        // 确保默认值设置
        {% if not carousel %}
        document.getElementById('link_url').value = '/';
        document.getElementById('is_active').checked = true;
        {% endif %}

        // 重新高亮首页按钮
        highlightActiveButton('/');
    }
}

// 表单验证
document.getElementById('carouselForm').addEventListener('submit', function(e) {
    const title = document.getElementById('title').value.trim();
    const images = document.getElementById('images').files;

    if (!title) {
        alert('请输入图片标题');
        e.preventDefault();
        return;
    }

    {% if not carousel %}
    if (!images || images.length === 0) {
        alert('请选择图片文件');
        e.preventDefault();
        return;
    }
    {% endif %}

    // 显示加载状态
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>处理中...';
    submitBtn.disabled = true;

    // 如果验证失败，恢复按钮状态
    setTimeout(() => {
        if (submitBtn.disabled) {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }, 10000);
});

// 字符计数
document.getElementById('title').addEventListener('input', function() {
    const maxLength = 100;
    const currentLength = this.value.length;
    const formText = this.nextElementSibling;

    if (currentLength > maxLength * 0.8) {
        formText.textContent = `${currentLength}/${maxLength} 字符`;
        formText.className = currentLength >= maxLength ? 'form-text text-danger' : 'form-text text-warning';
    } else {
        formText.textContent = '建议长度：10-50个字符';
        formText.className = 'form-text';
    }
});

document.getElementById('description').addEventListener('input', function() {
    const maxLength = 500;
    const currentLength = this.value.length;
    const formText = this.nextElementSibling;

    if (currentLength > maxLength * 0.8) {
        formText.textContent = `${currentLength}/${maxLength} 字符`;
        formText.className = currentLength >= maxLength ? 'form-text text-danger' : 'form-text text-warning';
    } else {
        formText.textContent = '可选，用于SEO和无障碍访问';
        formText.className = 'form-text';
    }
});
</script>
{% endblock %}

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>