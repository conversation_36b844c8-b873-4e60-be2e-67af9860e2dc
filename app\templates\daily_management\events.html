{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .event-type-badge {
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .event-emergency {
        background-color: #e74a3b;
        color: white;
    }

    .event-activity {
        background-color: #4e73df;
        color: white;
    }

    .event-inspection {
        background-color: #1cc88a;
        color: white;
    }

    .event-other {
        background-color: #f6c23e;
        color: white;
    }

    .action-buttons .btn {
        margin-right: 5px;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .description-cell {
        max-width: 300px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 导入导航宏 -->
    {% from 'daily_management/components/navigation.html' import daily_management_header %}

    <!-- 显示导航和学校信息 -->
    {{ daily_management_header(title, school, log, 'events') }}

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">特殊事件列表</h6>
            <div>
                <a href="{{ url_for('daily_management.add_event', log_id=log.id) }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus mr-1"></i> 添加特殊事件
                </a>
                <a href="{{ url_for('daily_management.print_events', log_id=log.id) }}" class="btn btn-success btn-sm">
                    <i class="fas fa-print mr-1"></i> 打印特殊事件
                </a>
                <a href="{{ url_for('daily_management.edit_log', date_str=log.log_date) }}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-left mr-1"></i> 返回日志
                </a>
            </div>
        </div>
        <div class="card-body">
            {% if events %}
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>事件类型</th>
                            <th>事件时间</th>
                            <th>描述</th>
                            <th>参与人员</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for event in events %}
                        <tr>
                            <td>
                                {% if '紧急' in event.event_type %}
                                <span class="event-type-badge event-emergency">{{ event.event_type }}</span>
                                {% elif '活动' in event.event_type %}
                                <span class="event-type-badge event-activity">{{ event.event_type }}</span>
                                {% elif '检查' in event.event_type %}
                                <span class="event-type-badge event-inspection">{{ event.event_type }}</span>
                                {% else %}
                                <span class="event-type-badge event-other">{{ event.event_type }}</span>
                                {% endif %}
                            </td>
                            <td>{{ event.event_time }}</td>
                            <td class="description-cell">{{ event.description|truncate(50) }}</td>
                            <td>{{ event.participants|truncate(30) if event.participants else '-' }}</td>
                            <td>
                                <a href="{{ url_for('daily_management.view_event', event_id=event.id) }}" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('daily_management.edit_event', event_id=event.id) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-danger btn-sm" data-toggle="modal" data-target="#deleteModal{{ event.id }}">
                                    <i class="fas fa-trash"></i>
                                </button>

                                <!-- 删除确认模态框 -->
                                <div class="modal fade" id="deleteModal{{ event.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel{{ event.id }}" aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ event.id }}">确认删除</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                确定要删除特殊事件"{{ event.event_type }}"吗？此操作不可恢复。
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                                                <form action="{{ url_for('daily_management.delete_event', event_id=event.id) }}" method="post">
                                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                    <button type="submit" class="btn btn-danger">确认删除</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-4">
                <p class="text-muted">暂无特殊事件记录</p>
                <a href="{{ url_for('daily_management.add_event', log_id=log.id) }}" class="btn btn-primary">
                    <i class="fas fa-plus mr-1"></i> 添加特殊事件
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
