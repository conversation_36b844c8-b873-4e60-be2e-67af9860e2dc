{% extends 'base.html' %}

{% block title %}系统监控 - {{ super() }}{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .monitor-card {
        margin-bottom: 20px;
    }
    .monitor-card .card-header {
        font-weight: bold;
    }
    .progress {
        height: 25px;
    }
    .progress-bar {
        font-weight: bold;
    }
    .system-info {
        font-family: monospace;
    }
    .log-level-info {
        color: #17a2b8;
    }
    .log-level-warning {
        color: #ffc107;
    }
    .log-level-error {
        color: #dc3545;
    }
    .log-level-critical {
        color: #721c24;
        font-weight: bold;
    }
    .refresh-btn {
        cursor: pointer;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>系统监控</h2>
        <p class="text-muted">监控系统运行状态和资源使用情况</p>
    </div>
    <div class="col-md-4 text-right">
        <button id="refreshMonitor" class="btn btn-info">
            <i class="fas fa-sync-alt"></i> 刷新数据
        </button>
        <a href="{{ url_for('system.settings') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回设置
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card monitor-card">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-server"></i> 系统资源
            </div>
            <div class="card-body">
                <h5>CPU 使用率</h5>
                <div class="progress mb-3">
                    <div class="progress-bar bg-{{ 'danger' if system_info.cpu_percent > 80 else 'warning' if system_info.cpu_percent > 60 else 'success' }}" 
                         role="progressbar" 
                         style="width: {{ system_info.cpu_percent }}%;" 
                         aria-valuenow="{{ system_info.cpu_percent }}" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                        {{ system_info.cpu_percent }}%
                    </div>
                </div>
                
                <h5>内存使用率</h5>
                <div class="progress mb-3">
                    <div class="progress-bar bg-{{ 'danger' if system_info.memory.percent > 80 else 'warning' if system_info.memory.percent > 60 else 'success' }}" 
                         role="progressbar" 
                         style="width: {{ system_info.memory.percent }}%;" 
                         aria-valuenow="{{ system_info.memory.percent }}" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                        {{ system_info.memory.percent }}%
                    </div>
                </div>
                <small class="text-muted">{{ system_info.memory.used }} GB / {{ system_info.memory.total }} GB</small>
                
                <h5 class="mt-3">磁盘使用率</h5>
                <div class="progress mb-3">
                    <div class="progress-bar bg-{{ 'danger' if system_info.disk.percent > 80 else 'warning' if system_info.disk.percent > 60 else 'success' }}" 
                         role="progressbar" 
                         style="width: {{ system_info.disk.percent }}%;" 
                         aria-valuenow="{{ system_info.disk.percent }}" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                        {{ system_info.disk.percent }}%
                    </div>
                </div>
                <small class="text-muted">{{ system_info.disk.used }} GB / {{ system_info.disk.total }} GB</small>
                
                <h5 class="mt-3">系统信息</h5>
                <table class="table table-sm system-info">
                    <tr>
                        <td>平台:</td>
                        <td>{{ system_info.platform }}</td>
                    </tr>
                    <tr>
                        <td>Python版本:</td>
                        <td>{{ system_info.python_version }}</td>
                    </tr>
                    <tr>
                        <td>CPU核心数:</td>
                        <td>{{ system_info.cpu_count }}</td>
                    </tr>
                    <tr>
                        <td>系统运行时间:</td>
                        <td>{{ system_info.uptime }}</td>
                    </tr>
                    <tr>
                        <td>进程ID:</td>
                        <td>{{ system_info.process.pid }}</td>
                    </tr>
                    <tr>
                        <td>进程内存占用:</td>
                        <td>{{ system_info.process.memory_percent }}%</td>
                    </tr>
                    <tr>
                        <td>进程CPU占用:</td>
                        <td>{{ system_info.process.cpu_percent }}%</td>
                    </tr>
                    <tr>
                        <td>进程线程数:</td>
                        <td>{{ system_info.process.threads }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card monitor-card">
            <div class="card-header bg-info text-white">
                <i class="fas fa-database"></i> 数据库统计
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center mb-4">
                        <div class="card">
                            <div class="card-body">
                                <h3>{{ system_info.database.users }}</h3>
                                <p class="text-muted mb-0">用户数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-center mb-4">
                        <div class="card">
                            <div class="card-body">
                                <h3>{{ system_info.database.audit_logs }}</h3>
                                <p class="text-muted mb-0">审计日志</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-center mb-4">
                        <div class="card">
                            <div class="card-body">
                                <h3>{{ system_info.database.system_logs }}</h3>
                                <p class="text-muted mb-0">系统日志</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card monitor-card">
            <div class="card-header bg-success text-white">
                <i class="fas fa-users"></i> 用户活动
            </div>
            <div class="card-body">
                <h5>最近登录</h5>
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>姓名</th>
                                <th>最后登录时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for login in user_activity.recent_logins %}
                            <tr>
                                <td>{{ login.username }}</td>
                                <td>{{ login.real_name }}</td>
                                <td>{{ login.last_login }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card monitor-card">
            <div class="card-header bg-warning text-white">
                <i class="fas fa-exclamation-triangle"></i> 系统日志
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>级别</th>
                                <th>模块</th>
                                <th>消息</th>
                                <th>时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in system_logs %}
                            <tr>
                                <td>
                                    {% if log.level == 'INFO' %}
                                    <span class="log-level-info"><i class="fas fa-info-circle"></i> {{ log.level }}</span>
                                    {% elif log.level == 'WARNING' %}
                                    <span class="log-level-warning"><i class="fas fa-exclamation-circle"></i> {{ log.level }}</span>
                                    {% elif log.level == 'ERROR' %}
                                    <span class="log-level-error"><i class="fas fa-times-circle"></i> {{ log.level }}</span>
                                    {% elif log.level == 'CRITICAL' %}
                                    <span class="log-level-critical"><i class="fas fa-skull-crossbones"></i> {{ log.level }}</span>
                                    {% else %}
                                    {{ log.level }}
                                    {% endif %}
                                </td>
                                <td>{{ log.module }}</td>
                                <td>{{ log.message }}</td>
                                <td>{{ log.created_at }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 刷新监控数据
        $('#refreshMonitor').click(function() {
            $(this).html('<i class="fas fa-spinner fa-spin"></i> 正在刷新...');
            $(this).prop('disabled', true);
            
            $.ajax({
                url: '{{ url_for("system.refresh_monitor") }}',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    // 刷新页面
                    location.reload();
                },
                error: function(xhr, status, error) {
                    alert('刷新数据失败: ' + error);
                    $('#refreshMonitor').html('<i class="fas fa-sync-alt"></i> 刷新数据');
                    $('#refreshMonitor').prop('disabled', false);
                }
            });
        });
    });
</script>
{% endblock %}
