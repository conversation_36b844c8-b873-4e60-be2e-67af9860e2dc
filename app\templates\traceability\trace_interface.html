{% extends 'base.html' %}

{% block title %}食材溯源接口{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .trace-chain {
        position: relative;
        padding: 20px 0;
    }
    .trace-step {
        display: flex;
        margin-bottom: 30px;
        position: relative;
    }
    .trace-step:not(:last-child):after {
        content: '';
        position: absolute;
        left: 25px;
        top: 50px;
        height: calc(100% - 25px);
        width: 2px;
        background-color: #dee2e6;
    }
    .trace-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        z-index: 1;
    }
    .trace-icon i {
        color: white;
        font-size: 20px;
    }
    .trace-content {
        flex: 1;
    }
    .trace-content h5 {
        margin-bottom: 10px;
    }
    .trace-card {
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        padding: 15px;
        margin-bottom: 15px;
        background-color: #fff;
    }
    .trace-detail {
        margin-top: 10px;
        padding-left: 15px;
        border-left: 2px solid #f0f0f0;
    }
    .trace-detail p {
        margin-bottom: 5px;
    }
    .trace-badge {
        font-size: 0.8em;
        padding: 3px 8px;
        border-radius: 10px;
        margin-left: 5px;
    }
    .trace-table {
        width: 100%;
        margin-top: 10px;
    }
    .trace-table th {
        background-color: #f8f9fa;
        padding: 8px;
    }
    .trace-table td {
        padding: 8px;
        border-top: 1px solid #dee2e6;
    }
    .trace-search-form {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .trace-result {
        display: none;
    }
    .loading {
        text-align: center;
        padding: 20px;
        display: none;
    }
    .loading i {
        font-size: 30px;
        color: #007bff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">食材溯源接口</h3>
                </div>
                <div class="card-body">
                    <!-- 溯源查询表单 -->
                    <div class="trace-search-form">
                        <form id="traceForm">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>溯源类型</label>
                                        <select class="form-control" id="traceType" name="trace_type" required>
                                            <option value="">请选择溯源类型</option>
                                            <option value="menu_plan">菜单计划</option>
                                            <option value="consumption_plan">消耗计划</option>
                                            <option value="stock_out">出库单</option>
                                            <option value="ingredient">食材</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>ID</label>
                                        <input type="number" class="form-control" id="traceId" name="trace_id" required>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>食材ID（可选）</label>
                                        <input type="number" class="form-control" id="ingredientId" name="ingredient_id">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>供应商ID（可选）</label>
                                        <input type="number" class="form-control" id="supplierId" name="supplier_id">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>开始日期（可选）</label>
                                        <input type="date" class="form-control" id="startDate" name="start_date">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>结束日期（可选）</label>
                                        <input type="date" class="form-control" id="endDate" name="end_date">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>区域ID（可选）</label>
                                        <input type="number" class="form-control" id="areaId" name="area_id">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <button type="submit" class="btn btn-primary btn-block">查询</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 加载中提示 -->
                    <div class="loading" id="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>正在查询溯源信息，请稍候...</p>
                    </div>

                    <!-- 溯源结果 -->
                    <div class="trace-result" id="traceResult">
                        <div class="alert alert-info">
                            <h4><i class="fas fa-info-circle"></i> 溯源结果</h4>
                            <p>以下是溯源查询的结果，展示了从菜单计划到食材供应商的完整溯源链。</p>
                        </div>

                        <div class="trace-chain">
                            <!-- 溯源链将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 溯源表单提交
        $('#traceForm').on('submit', function(e) {
            e.preventDefault();
            
            // 显示加载中
            $('#loading').show();
            $('#traceResult').hide();
            
            // 获取表单数据
            var formData = $(this).serialize();
            
            // 发送AJAX请求
            $.ajax({
                url: '/api/trace',
                type: 'GET',
                data: formData,
                success: function(response) {
                    // 隐藏加载中
                    $('#loading').hide();
                    
                    // 处理溯源结果
                    processTraceResult(response);
                    
                    // 显示结果
                    $('#traceResult').show();
                },
                error: function(xhr) {
                    // 隐藏加载中
                    $('#loading').hide();
                    
                    // 显示错误信息
                    var errorMsg = '查询失败';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMsg = xhr.responseJSON.error;
                    }
                    
                    alert('溯源查询失败: ' + errorMsg);
                }
            });
        });
        
        // 处理溯源结果
        function processTraceResult(data) {
            var traceChain = $('.trace-chain');
            traceChain.empty();
            
            var traceType = $('#traceType').val();
            
            // 根据溯源类型生成不同的溯源链
            switch(traceType) {
                case 'menu_plan':
                    generateMenuPlanTrace(data, traceChain);
                    break;
                case 'consumption_plan':
                    generateConsumptionPlanTrace(data, traceChain);
                    break;
                case 'stock_out':
                    generateStockOutTrace(data, traceChain);
                    break;
                case 'ingredient':
                    generateIngredientTrace(data, traceChain);
                    break;
            }
        }
        
        // 生成菜单计划溯源链
        function generateMenuPlanTrace(data, container) {
            // 1. 菜单计划
            if (data.menu_plan) {
                var menuPlanHtml = `
                    <div class="trace-step">
                        <div class="trace-icon bg-primary">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="trace-content">
                            <h5>菜单计划 <span class="badge badge-${getStatusClass(data.menu_plan.status)}">${data.menu_plan.status}</span></h5>
                            <div class="trace-card">
                                <p><strong>ID:</strong> ${data.menu_plan.id}</p>
                                <p><strong>日期:</strong> ${data.menu_plan.plan_date}</p>
                                <p><strong>餐次:</strong> ${data.menu_plan.meal_type}</p>
                                <p><strong>区域:</strong> ${data.menu_plan.area_name || '未知'}</p>
                            </div>
                        </div>
                    </div>
                `;
                container.append(menuPlanHtml);
            }
            
            // 2. 消耗计划
            if (data.consumption_plans && data.consumption_plans.length > 0) {
                var consumptionPlansHtml = `
                    <div class="trace-step">
                        <div class="trace-icon bg-info">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="trace-content">
                            <h5>消耗计划</h5>
                `;
                
                data.consumption_plans.forEach(function(plan) {
                    consumptionPlansHtml += `
                        <div class="trace-card">
                            <p><strong>ID:</strong> ${plan.id}</p>
                            <p><strong>状态:</strong> <span class="badge badge-${getStatusClass(plan.status)}">${plan.status}</span></p>
                            <p><strong>创建时间:</strong> ${plan.created_at}</p>
                            <p><strong>创建人:</strong> ${plan.creator || '未知'}</p>
                        </div>
                    `;
                });
                
                consumptionPlansHtml += `
                        </div>
                    </div>
                `;
                container.append(consumptionPlansHtml);
            }
            
            // 3. 出库单
            if (data.stock_outs && data.stock_outs.length > 0) {
                var stockOutsHtml = `
                    <div class="trace-step">
                        <div class="trace-icon bg-warning">
                            <i class="fas fa-dolly"></i>
                        </div>
                        <div class="trace-content">
                            <h5>出库单</h5>
                `;
                
                data.stock_outs.forEach(function(stockOut) {
                    stockOutsHtml += `
                        <div class="trace-card">
                            <p><strong>ID:</strong> ${stockOut.id}</p>
                            <p><strong>出库单号:</strong> ${stockOut.stock_out_number}</p>
                            <p><strong>出库日期:</strong> ${stockOut.stock_out_date}</p>
                            <p><strong>类型:</strong> ${stockOut.stock_out_type}</p>
                            <p><strong>状态:</strong> <span class="badge badge-${getStatusClass(stockOut.status)}">${stockOut.status}</span></p>
                            <p><strong>操作人:</strong> ${stockOut.operator || '未知'}</p>
                        </div>
                    `;
                });
                
                stockOutsHtml += `
                        </div>
                    </div>
                `;
                container.append(stockOutsHtml);
            }
            
            // 4. 库存和入库信息
            if (data.inventory_data && data.inventory_data.length > 0) {
                var inventoryHtml = `
                    <div class="trace-step">
                        <div class="trace-icon bg-success">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="trace-content">
                            <h5>库存和入库信息</h5>
                            <div class="trace-card">
                                <table class="trace-table">
                                    <thead>
                                        <tr>
                                            <th>批次号</th>
                                            <th>食材</th>
                                            <th>数量</th>
                                            <th>入库单号</th>
                                            <th>入库日期</th>
                                            <th>供应商</th>
                                            <th>生产日期</th>
                                            <th>过期日期</th>
                                            <th>质检结果</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                `;
                
                data.inventory_data.forEach(function(item) {
                    inventoryHtml += `
                        <tr>
                            <td>${item.batch_number || '未知'}</td>
                            <td>${item.ingredient_name || '未知'}</td>
                            <td>${item.quantity} ${item.unit}</td>
                            <td>${item.stock_in_number || '未知'}</td>
                            <td>${item.stock_in_date || '未知'}</td>
                            <td>${item.supplier_name || '未知'}</td>
                            <td>${item.production_date || '未知'}</td>
                            <td>${item.expiry_date || '未知'}</td>
                            <td>${item.quality_check_result || '未知'}</td>
                        </tr>
                    `;
                });
                
                inventoryHtml += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
                container.append(inventoryHtml);
            }
        }
        
        // 根据状态获取对应的Bootstrap样式类
        function getStatusClass(status) {
            switch(status) {
                case '计划中':
                    return 'secondary';
                case '已审核':
                    return 'info';
                case '已执行':
                    return 'success';
                case '已取消':
                    return 'danger';
                case '已发布':
                    return 'primary';
                case '待审核':
                    return 'warning';
                case '已出库':
                    return 'success';
                default:
                    return 'secondary';
            }
        }
        
        // 其他溯源链生成函数将在后续实现
    });
</script>
{% endblock %}
