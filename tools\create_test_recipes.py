#!/usr/bin/env python3
"""
创建测试菜品数据
用于测试周菜单功能中的学校/系统分类显示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models import Recipe, AdministrativeArea, User
from sqlalchemy import text

def create_test_recipes():
    """创建测试菜品数据"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🍽️ 创建测试菜品数据...")
            
            # 获取第一个学校区域
            school_area = AdministrativeArea.query.filter_by(level=3).first()
            if not school_area:
                print("❌ 未找到学校区域，请先创建学校数据")
                return False
            
            # 获取第一个用户作为创建者
            user = User.query.first()
            if not user:
                print("❌ 未找到用户，请先创建用户数据")
                return False
            
            print(f"📍 使用学校: {school_area.name}")
            print(f"👤 创建者: {user.name}")
            
            # 系统菜品（全局菜品）
            system_recipes = [
                {"name": "白米饭", "category": "主食"},
                {"name": "小米粥", "category": "主食"},
                {"name": "馒头", "category": "主食"},
                {"name": "花卷", "category": "主食"},
                {"name": "红烧肉", "category": "肉类"},
                {"name": "糖醋排骨", "category": "肉类"},
                {"name": "宫保鸡丁", "category": "肉类"},
                {"name": "清炒白菜", "category": "素菜"},
                {"name": "麻婆豆腐", "category": "素菜"},
                {"name": "西红柿鸡蛋", "category": "素菜"},
                {"name": "青椒土豆丝", "category": "素菜"},
                {"name": "紫菜蛋花汤", "category": "汤品"},
                {"name": "冬瓜汤", "category": "汤品"},
                {"name": "银耳莲子汤", "category": "汤品"},
            ]
            
            # 学校专用菜品
            school_recipes = [
                {"name": f"{school_area.name}特色炒饭", "category": "主食"},
                {"name": f"{school_area.name}营养面条", "category": "主食"},
                {"name": "本校红烧鱼", "category": "肉类"},
                {"name": "特色炖鸡", "category": "肉类"},
                {"name": "学校时蔬", "category": "素菜"},
                {"name": "营养豆腐", "category": "素菜"},
                {"name": "校园汤品", "category": "汤品"},
            ]
            
            created_count = 0
            
            # 创建系统菜品
            print("\n📦 创建系统菜品...")
            for recipe_data in system_recipes:
                # 检查是否已存在
                existing = Recipe.query.filter_by(
                    name=recipe_data["name"],
                    is_global=True
                ).first()
                
                if existing:
                    print(f"  ⏭️  跳过已存在的系统菜品: {recipe_data['name']}")
                    continue
                
                recipe = Recipe(
                    name=recipe_data["name"],
                    category=recipe_data["category"],
                    area_id=None,  # 系统菜品不绑定学校
                    is_global=True,  # 标记为全局菜品
                    is_user_defined=False,
                    status=1,
                    created_by=user.id,
                    description=f"系统预设的{recipe_data['category']}菜品"
                )
                
                db.session.add(recipe)
                created_count += 1
                print(f"  ✅ 创建系统菜品: {recipe_data['name']} ({recipe_data['category']})")
            
            # 创建学校专用菜品
            print(f"\n🏫 创建{school_area.name}专用菜品...")
            for recipe_data in school_recipes:
                # 检查是否已存在
                existing = Recipe.query.filter_by(
                    name=recipe_data["name"],
                    area_id=school_area.id
                ).first()
                
                if existing:
                    print(f"  ⏭️  跳过已存在的学校菜品: {recipe_data['name']}")
                    continue
                
                recipe = Recipe(
                    name=recipe_data["name"],
                    category=recipe_data["category"],
                    area_id=school_area.id,  # 绑定到学校
                    is_global=False,
                    is_user_defined=True,  # 标记为用户自定义
                    status=1,
                    created_by=user.id,
                    description=f"{school_area.name}专用的{recipe_data['category']}菜品"
                )
                
                db.session.add(recipe)
                created_count += 1
                print(f"  ✅ 创建学校菜品: {recipe_data['name']} ({recipe_data['category']})")
            
            # 提交事务
            db.session.commit()
            
            # 统计结果
            print(f"\n📊 创建完成，共创建 {created_count} 个菜品")
            
            # 验证数据
            print("\n🔍 验证创建的数据:")
            
            system_count = Recipe.query.filter_by(is_global=True).count()
            school_count = Recipe.query.filter_by(area_id=school_area.id).count()
            
            print(f"  📦 系统菜品: {system_count} 个")
            print(f"  🏫 {school_area.name}菜品: {school_count} 个")
            
            # 按分类统计
            print("\n📋 按分类统计:")
            categories = db.session.query(
                Recipe.category,
                db.func.count(Recipe.id)
            ).group_by(Recipe.category).all()
            
            for category, count in categories:
                print(f"  📂 {category}: {count} 个")
            
            print("\n" + "=" * 60)
            print("🎉 测试菜品数据创建完成！")
            print("\n📋 现在可以：")
            print("1. 访问周菜单计划页面")
            print("2. 点击菜单格子选择菜品")
            print("3. 查看学校菜品和系统菜品的分类显示")
            print(f"4. 测试URL: http://127.0.0.1:5000/weekly-menu-v2/plan?area_id={school_area.id}")
            
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 创建测试菜品失败: {str(e)}")
            return False

if __name__ == '__main__':
    success = create_test_recipes()
    if success:
        print("\n✅ 脚本执行成功")
    else:
        print("\n❌ 脚本执行失败")
        sys.exit(1)
