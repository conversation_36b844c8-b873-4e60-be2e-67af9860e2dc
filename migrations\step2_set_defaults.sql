-- 步骤2：设置默认值和约束
-- 请在步骤1成功后执行

PRINT '=== 步骤2：设置默认值和约束 ===';

-- 1. 为食材表 is_global 字段设置默认值
BEGIN TRY
    UPDATE ingredients SET is_global = 0 WHERE is_global IS NULL;
    PRINT '✓ 食材表 is_global 字段默认值设置成功';
    
    -- 修改为 NOT NULL
    ALTER TABLE ingredients ALTER COLUMN is_global BIT NOT NULL;
    PRINT '✓ 食材表 is_global 字段修改为 NOT NULL';
END TRY
BEGIN CATCH
    PRINT '❌ 食材表 is_global 字段设置失败: ' + ERROR_MESSAGE();
END CATCH

-- 2. 为食谱表 is_global 字段设置默认值
BEGIN TRY
    UPDATE recipes SET is_global = 0 WHERE is_global IS NULL;
    PRINT '✓ 食谱表 is_global 字段默认值设置成功';
    
    -- 修改为 NOT NULL
    ALTER TABLE recipes ALTER COLUMN is_global BIT NOT NULL;
    PRINT '✓ 食谱表 is_global 字段修改为 NOT NULL';
END TRY
BEGIN CATCH
    PRINT '❌ 食谱表 is_global 字段设置失败: ' + ERROR_MESSAGE();
END CATCH

-- 3. 将现有数据标记为全局数据
BEGIN TRY
    UPDATE ingredients SET is_global = 1 WHERE area_id IS NULL;
    PRINT '✓ 现有食材已标记为全局数据';
END TRY
BEGIN CATCH
    PRINT '❌ 食材数据更新失败: ' + ERROR_MESSAGE();
END CATCH

BEGIN TRY
    UPDATE recipes SET is_global = 1 WHERE area_id IS NULL;
    PRINT '✓ 现有食谱已标记为全局数据';
END TRY
BEGIN CATCH
    PRINT '❌ 食谱数据更新失败: ' + ERROR_MESSAGE();
END CATCH

PRINT '';
PRINT '=== 步骤2完成 ===';

-- 验证数据更新结果
SELECT 
    '食材统计' as 类型,
    COUNT(*) as 总数,
    SUM(CASE WHEN is_global = 1 THEN 1 ELSE 0 END) as 全局数量,
    SUM(CASE WHEN area_id IS NOT NULL THEN 1 ELSE 0 END) as 学校专用数量
FROM ingredients

UNION ALL

SELECT 
    '食谱统计' as 类型,
    COUNT(*) as 总数,
    SUM(CASE WHEN is_global = 1 THEN 1 ELSE 0 END) as 全局数量,
    SUM(CASE WHEN area_id IS NOT NULL THEN 1 ELSE 0 END) as 学校专用数量
FROM recipes;
