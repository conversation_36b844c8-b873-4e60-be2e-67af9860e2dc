<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>陪餐记录打印</title>
    <style nonce="{{ csp_nonce }}">
        @media print {
            @page {
                size: A4;
                margin: 1cm;
            }
            body {
                font-family: "SimSun", "宋体", serif;
                line-height: 1.5;
                color: #000;
                background: #fff;
                margin: 0;
                padding: 0;
                font-size: 12pt;
            }
            .container {
                width: 100%;
                max-width: 210mm;
                margin: 0 auto;
                padding: 0;
            }
            .header {
                text-align: center;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #000;
            }
            .header h1 {
                font-size: 24pt;
                font-weight: bold;
                margin: 0;
                padding: 10px 0;
            }
            .header p {
                font-size: 12pt;
                margin: 5px 0;
            }
            .section {
                margin-bottom: 20px;
            }
            .section-title {
                font-size: 16pt;
                font-weight: bold;
                margin-bottom: 10px;
                padding-bottom: 5px;
                border-bottom: 1px solid #000;
            }
            .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }
            .info-item {
                margin-bottom: 10px;
            }
            .info-label {
                font-weight: bold;
                display: inline-block;
                min-width: 80px;
            }
            .info-value {
                display: inline-block;
            }
            .rating {
                display: inline-block;
            }
            .star {
                display: inline-block;
                width: 16px;
                height: 16px;
                background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path fill="currentColor" d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"/></svg>');
                background-repeat: no-repeat;
                background-size: contain;
            }
            .star-empty {
                background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path fill="currentColor" d="M528.1 171.5L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6zM388.6 312.3l23.7 138.4L288 385.4l-124.3 65.3 23.7-138.4-100.6-98 139-20.2 62.2-126 62.2 126 139 20.2-100.6 98z"/></svg>');
            }
            .text-box {
                border: 1px solid #ccc;
                padding: 10px;
                margin-top: 5px;
                min-height: 60px;
                background-color: #f9f9f9;
            }
            .photo-section {
                margin-top: 20px;
            }
            .photo-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }
            .photo-item {
                text-align: center;
            }
            .photo-item img {
                max-width: 100%;
                max-height: 200px;
                border: 1px solid #ccc;
                padding: 5px;
                background: white;
            }
            .photo-caption {
                margin-top: 5px;
                font-size: 10pt;
                color: #666;
            }
            .footer {
                margin-top: 30px;
                text-align: right;
                font-size: 10pt;
                color: #666;
            }
            .school-info {
                text-align: center;
                margin-bottom: 10px;
            }
            .school-name {
                font-size: 14pt;
                font-weight: bold;
            }
            .qr-code {
                text-align: center;
                margin-top: 20px;
            }
            .qr-code img {
                width: 100px;
                height: 100px;
            }
            .qr-code-caption {
                font-size: 9pt;
                color: #666;
                margin-top: 5px;
            }
            .no-print {
                display: none;
            }
        }
        
        /* 非打印样式 */
        body {
            font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
            line-height: 1.5;
            color: #333;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            width: 100%;
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #333;
        }
        .header h1 {
            font-size: 24pt;
            font-weight: bold;
            margin: 0;
            padding: 10px 0;
        }
        .header p {
            font-size: 12pt;
            margin: 5px 0;
        }
        .section {
            margin-bottom: 20px;
        }
        .section-title {
            font-size: 16pt;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #333;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            display: inline-block;
            min-width: 80px;
        }
        .info-value {
            display: inline-block;
        }
        .rating {
            display: inline-block;
        }
        .star {
            color: #FFD700;
            display: inline-block;
        }
        .text-box {
            border: 1px solid #ccc;
            padding: 10px;
            margin-top: 5px;
            min-height: 60px;
            background-color: #f9f9f9;
        }
        .photo-section {
            margin-top: 20px;
        }
        .photo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .photo-item {
            text-align: center;
        }
        .photo-item img {
            max-width: 100%;
            max-height: 200px;
            border: 1px solid #ccc;
            padding: 5px;
            background: white;
        }
        .photo-caption {
            margin-top: 5px;
            font-size: 10pt;
            color: #666;
        }
        .footer {
            margin-top: 30px;
            text-align: right;
            font-size: 10pt;
            color: #666;
        }
        .school-info {
            text-align: center;
            margin-bottom: 10px;
        }
        .school-name {
            font-size: 14pt;
            font-weight: bold;
        }
        .qr-code {
            text-align: center;
            margin-top: 20px;
        }
        .qr-code img {
            width: 100px;
            height: 100px;
        }
        .qr-code-caption {
            font-size: 9pt;
            color: #666;
            margin-top: 5px;
        }
        .no-print {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 20px 0;
            cursor: pointer;
            border: none;
            border-radius: 4px;
        }
        .no-print:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="school-info">
                <div class="school-name">{{ companion.daily_log.area.name }}</div>
            </div>
            <h1>陪餐记录表</h1>
            <p>记录编号：{{ companion.id }}</p>
        </div>

        <div class="section">
            <div class="section-title">基本信息</div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">陪餐人：</span>
                    <span class="info-value">{{ companion.companion_name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">角色：</span>
                    <span class="info-value">{{ companion.companion_role }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">餐次：</span>
                    <span class="info-value">
                        {% if companion.meal_type == 'breakfast' %}早餐
                        {% elif companion.meal_type == 'lunch' %}午餐
                        {% elif companion.meal_type == 'dinner' %}晚餐
                        {% endif %}
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">陪餐时间：</span>
                    <span class="info-value">{{ companion.dining_time }}</span>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">评分情况</div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">口味评分：</span>
                    <span class="info-value">
                        <span class="rating">
                            {% if companion.taste_rating %}
                                {% for i in range(companion.taste_rating) %}
                                    <span class="star">★</span>
                                {% endfor %}
                                {% for i in range(5 - companion.taste_rating) %}
                                    <span class="star star-empty">☆</span>
                                {% endfor %}
                            {% else %}
                                未评分
                            {% endif %}
                        </span>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">卫生评分：</span>
                    <span class="info-value">
                        <span class="rating">
                            {% if companion.hygiene_rating %}
                                {% for i in range(companion.hygiene_rating) %}
                                    <span class="star">★</span>
                                {% endfor %}
                                {% for i in range(5 - companion.hygiene_rating) %}
                                    <span class="star star-empty">☆</span>
                                {% endfor %}
                            {% else %}
                                未评分
                            {% endif %}
                        </span>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">服务评分：</span>
                    <span class="info-value">
                        <span class="rating">
                            {% if companion.service_rating %}
                                {% for i in range(companion.service_rating) %}
                                    <span class="star">★</span>
                                {% endfor %}
                                {% for i in range(5 - companion.service_rating) %}
                                    <span class="star star-empty">☆</span>
                                {% endfor %}
                            {% else %}
                                未评分
                            {% endif %}
                        </span>
                    </span>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">评价与建议</div>
            <div class="info-item">
                <div class="info-label">评价意见：</div>
                <div class="text-box">{{ companion.comments or '无' }}</div>
            </div>
            <div class="info-item" style="margin-top: 15px;">
                <div class="info-label">改进建议：</div>
                <div class="text-box">{{ companion.suggestions or '无' }}</div>
            </div>
        </div>

        {% if photos %}
        <div class="section photo-section">
            <div class="section-title">陪餐照片</div>
            <div class="photo-grid">
                {% for photo in photos %}
                <div class="photo-item">
                    <img src="{{ photo.file_path }}" alt="陪餐照片">
                    <div class="photo-caption">陪餐照片 #{{ loop.index }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <div class="footer">
            <p>打印时间：{{ now.strftime('%Y年%m月%d日 %H:%M') }}</p>
            <p>陪餐人签名：________________</p>
        </div>
    </div>

    <div style="text-align: center; margin-top: 20px;">
        <button class="no-print" class="print-button">打印此页</button>
        <a class="no-print" href="{{ url_for('daily_management.view_companion', companion_id=companion.id) }}" style="margin-left: 10px; text-decoration: none; color: #333;">返回详情页</a>
    </div>
</body>
</html>
