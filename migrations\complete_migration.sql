-- 完成学校级隔离迁移 - 设置默认值和更新数据
PRINT '=== 完成学校级隔离迁移 ===';

-- 1. 更新食材表的 is_global 字段
BEGIN TRY
    -- 将所有 NULL 值设置为 0（非全局）
    UPDATE ingredients SET is_global = 0 WHERE is_global IS NULL;
    PRINT '✓ 食材表 is_global 字段 NULL 值已设置为 0';
    
    -- 将没有 area_id 的食材标记为全局食材
    UPDATE ingredients SET is_global = 1 WHERE area_id IS NULL;
    PRINT '✓ 现有食材已标记为全局数据';
    
    -- 修改字段为 NOT NULL（如果还不是的话）
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'ingredients' 
               AND COLUMN_NAME = 'is_global' 
               AND IS_NULLABLE = 'YES')
    BEGIN
        ALTER TABLE ingredients ALTER COLUMN is_global BIT NOT NULL;
        PRINT '✓ 食材表 is_global 字段已设置为 NOT NULL';
    END
    ELSE
    BEGIN
        PRINT '- 食材表 is_global 字段已经是 NOT NULL';
    END
    
END TRY
BEGIN CATCH
    PRINT '❌ 食材表更新失败: ' + ERROR_MESSAGE();
END CATCH

-- 2. 更新食谱表的 is_global 字段
BEGIN TRY
    -- 将所有 NULL 值设置为 0（非全局）
    UPDATE recipes SET is_global = 0 WHERE is_global IS NULL;
    PRINT '✓ 食谱表 is_global 字段 NULL 值已设置为 0';
    
    -- 将没有 area_id 的食谱标记为全局食谱
    UPDATE recipes SET is_global = 1 WHERE area_id IS NULL;
    PRINT '✓ 现有食谱已标记为全局数据';
    
    -- 修改字段为 NOT NULL（如果还不是的话）
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'recipes' 
               AND COLUMN_NAME = 'is_global' 
               AND IS_NULLABLE = 'YES')
    BEGIN
        ALTER TABLE recipes ALTER COLUMN is_global BIT NOT NULL;
        PRINT '✓ 食谱表 is_global 字段已设置为 NOT NULL';
    END
    ELSE
    BEGIN
        PRINT '- 食谱表 is_global 字段已经是 NOT NULL';
    END
    
END TRY
BEGIN CATCH
    PRINT '❌ 食谱表更新失败: ' + ERROR_MESSAGE();
END CATCH

-- 3. 添加默认约束（如果需要的话）
BEGIN TRY
    -- 为食材表添加默认约束
    IF NOT EXISTS (SELECT * FROM sys.default_constraints 
                   WHERE parent_object_id = OBJECT_ID('ingredients') 
                   AND parent_column_id = (SELECT column_id FROM sys.columns 
                                         WHERE object_id = OBJECT_ID('ingredients') 
                                         AND name = 'is_global'))
    BEGIN
        ALTER TABLE ingredients ADD CONSTRAINT DF_ingredients_is_global DEFAULT 0 FOR is_global;
        PRINT '✓ 食材表 is_global 字段默认约束已添加';
    END
    ELSE
    BEGIN
        PRINT '- 食材表 is_global 字段默认约束已存在';
    END
    
    -- 为食谱表添加默认约束
    IF NOT EXISTS (SELECT * FROM sys.default_constraints 
                   WHERE parent_object_id = OBJECT_ID('recipes') 
                   AND parent_column_id = (SELECT column_id FROM sys.columns 
                                         WHERE object_id = OBJECT_ID('recipes') 
                                         AND name = 'is_global'))
    BEGIN
        ALTER TABLE recipes ADD CONSTRAINT DF_recipes_is_global DEFAULT 0 FOR is_global;
        PRINT '✓ 食谱表 is_global 字段默认约束已添加';
    END
    ELSE
    BEGIN
        PRINT '- 食谱表 is_global 字段默认约束已存在';
    END
    
END TRY
BEGIN CATCH
    PRINT '❌ 默认约束添加失败: ' + ERROR_MESSAGE();
END CATCH

PRINT '';
PRINT '=== 迁移完成！验证结果 ===';

-- 4. 验证最终结果
SELECT 
    '食材统计' as 类型,
    COUNT(*) as 总数,
    SUM(CASE WHEN is_global = 1 THEN 1 ELSE 0 END) as 全局数量,
    SUM(CASE WHEN is_global = 0 THEN 1 ELSE 0 END) as 学校专用数量,
    SUM(CASE WHEN area_id IS NOT NULL THEN 1 ELSE 0 END) as 有学校ID数量,
    SUM(CASE WHEN area_id IS NULL THEN 1 ELSE 0 END) as 无学校ID数量
FROM ingredients

UNION ALL

SELECT 
    '食谱统计' as 类型,
    COUNT(*) as 总数,
    SUM(CASE WHEN is_global = 1 THEN 1 ELSE 0 END) as 全局数量,
    SUM(CASE WHEN is_global = 0 THEN 1 ELSE 0 END) as 学校专用数量,
    SUM(CASE WHEN area_id IS NOT NULL THEN 1 ELSE 0 END) as 有学校ID数量,
    SUM(CASE WHEN area_id IS NULL THEN 1 ELSE 0 END) as 无学校ID数量
FROM recipes;

-- 5. 验证字段属性
SELECT 
    '字段验证' as 类型,
    TABLE_NAME as 表名,
    COLUMN_NAME as 字段名,
    DATA_TYPE as 数据类型,
    IS_NULLABLE as 可为空,
    COLUMN_DEFAULT as 默认值
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME IN ('ingredients', 'recipes') 
AND COLUMN_NAME IN ('area_id', 'is_global')
ORDER BY TABLE_NAME, COLUMN_NAME;

PRINT '';
PRINT '=== 学校级隔离迁移全部完成！ ===';
PRINT '现在可以重启应用并测试功能了！';
