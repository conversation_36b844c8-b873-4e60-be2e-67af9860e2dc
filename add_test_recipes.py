#!/usr/bin/env python3
"""
快速添加测试菜品数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Recipe, AdministrativeArea, User
from sqlalchemy import text

def add_test_recipes():
    """添加测试菜品数据"""
    app = create_app()

    with app.app_context():
        try:
            print("🍽️ 添加测试菜品数据...")

            # 获取第一个学校区域
            school_area = AdministrativeArea.query.filter_by(level=3).first()
            if not school_area:
                print("❌ 未找到学校区域")
                return False

            # 获取第一个用户作为创建者
            user = User.query.first()
            if not user:
                print("❌ 未找到用户")
                return False

            print(f"📍 使用学校: {school_area.name}")

            # 系统菜品（全局菜品）
            system_recipes = [
                {"name": "白米饭", "category": "主食"},
                {"name": "小米粥", "category": "主食"},
                {"name": "红烧肉", "category": "肉类"},
                {"name": "糖醋排骨", "category": "肉类"},
                {"name": "清炒白菜", "category": "素菜"},
                {"name": "麻婆豆腐", "category": "素菜"},
                {"name": "紫菜蛋花汤", "category": "汤品"},
                {"name": "冬瓜汤", "category": "汤品"},
            ]

            # 学校专用菜品
            school_recipes = [
                {"name": "学校炒饭", "category": "主食"},
                {"name": "营养面条", "category": "主食"},
                {"name": "本校红烧鱼", "category": "肉类"},
                {"name": "特色炖鸡", "category": "肉类"},
                {"name": "学校时蔬", "category": "素菜"},
                {"name": "营养豆腐", "category": "素菜"},
                {"name": "校园汤品", "category": "汤品"},
            ]

            created_count = 0

            # 使用原始SQL创建系统菜品
            print("\n📦 创建系统菜品...")
            for recipe_data in system_recipes:
                # 检查是否已存在
                check_sql = text("SELECT COUNT(*) as count FROM recipes WHERE name = :name")
                result = db.session.execute(check_sql, {"name": recipe_data["name"]}).fetchone()

                if result.count > 0:
                    print(f"  ⏭️  跳过: {recipe_data['name']}")
                    continue

                # 使用原始SQL插入
                insert_sql = text("""
                    INSERT INTO recipes (name, category, area_id, is_global, is_user_defined, status, created_by, description, created_at, updated_at)
                    VALUES (:name, :category, NULL, 1, 0, 1, :created_by, :description, GETDATE(), GETDATE())
                """)

                db.session.execute(insert_sql, {
                    "name": recipe_data["name"],
                    "category": recipe_data["category"],
                    "created_by": user.id,
                    "description": f"系统{recipe_data['category']}"
                })

                created_count += 1
                print(f"  ✅ {recipe_data['name']} ({recipe_data['category']})")

            # 使用原始SQL创建学校专用菜品
            print(f"\n🏫 创建学校菜品...")
            for recipe_data in school_recipes:
                # 检查是否已存在
                check_sql = text("SELECT COUNT(*) as count FROM recipes WHERE name = :name")
                result = db.session.execute(check_sql, {"name": recipe_data["name"]}).fetchone()

                if result.count > 0:
                    print(f"  ⏭️  跳过: {recipe_data['name']}")
                    continue

                # 使用原始SQL插入
                insert_sql = text("""
                    INSERT INTO recipes (name, category, area_id, is_global, is_user_defined, status, created_by, description, created_at, updated_at)
                    VALUES (:name, :category, :area_id, 0, 1, 1, :created_by, :description, GETDATE(), GETDATE())
                """)

                db.session.execute(insert_sql, {
                    "name": recipe_data["name"],
                    "category": recipe_data["category"],
                    "area_id": school_area.id,
                    "created_by": user.id,
                    "description": f"学校{recipe_data['category']}"
                })

                created_count += 1
                print(f"  ✅ {recipe_data['name']} ({recipe_data['category']})")

            db.session.commit()

            print(f"\n📊 共创建 {created_count} 个菜品")

            # 验证数据
            system_count = Recipe.query.filter_by(is_global=True).count()
            school_count = Recipe.query.filter_by(area_id=school_area.id).count()

            print(f"📦 系统菜品: {system_count} 个")
            print(f"🏫 学校菜品: {school_count} 个")

            print(f"\n🌐 测试URL: http://127.0.0.1:5000/weekly-menu-v2/plan?area_id={school_area.id}")

            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ 失败: {str(e)}")
            return False

if __name__ == '__main__':
    add_test_recipes()
