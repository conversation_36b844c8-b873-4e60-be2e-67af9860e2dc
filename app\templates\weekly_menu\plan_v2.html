{% extends "base.html" %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .menu-input {
        min-height: 100px;
        cursor: pointer;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 8px;
        margin-bottom: 10px;
        background-color: #f9f9f9;
    }

    .menu-input.readonly {
        background-color: #f0f0f0;
        cursor: not-allowed;
    }

    .menu-table {
        width: 100%;
        border-collapse: collapse;
    }

    .menu-table th, .menu-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: center;
    }

    .menu-table th {
        background-color: #f2f2f2;
    }

    .week-selector {
        display: flex;
        margin-bottom: 20px;
        overflow-x: auto;
    }

    .week-item {
        padding: 10px 15px;
        margin-right: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        white-space: nowrap;
        position: relative;
    }

    .week-item.active {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    .week-item.not-editable {
        background-color: #f0f0f0;
    }

    /* 已发布状态的周菜单使用红色显示 */
    .week-item[data-status="已发布"] {
        background-color: #dc3545;
        color: white;
        border-color: #dc3545;
    }

    .selected-recipe-tag {
        display: inline-block;
        background-color: #e9ecef;
        padding: 5px 10px;
        margin: 5px;
        border-radius: 20px;
        transition: all 0.3s;
    }

    .selected-recipe-tag .remove-btn {
        margin-left: 5px;
        cursor: pointer;
        font-weight: bold;
    }

    /* 高亮效果 */
    .selected-recipe-tag.highlight {
        background-color: #f8d7da;
        transform: scale(1.1);
        box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
    }

    .recipe-card {
        margin-bottom: 15px;
        cursor: pointer;
    }

    .recipe-card:hover {
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }

    .recipe-card .card-body {
        padding: 10px;
        text-align: center;
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 2s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .save-status {
        display: none;
        padding: 10px;
        margin-top: 10px;
        border-radius: 4px;
    }

    .save-status.success {
        background-color: #d4edda;
        color: #155724;
    }

    .save-status.error {
        background-color: #f8d7da;
        color: #721c24;
    }

    .save-status.info {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    /* 菜品显示项样式 */
    .recipe-item-display {
        display: inline-block;
        background-color: #e9ecef;
        padding: 4px 8px;
        margin: 2px;
        border-radius: 4px;
        font-size: 0.9rem;
        line-height: 1.2;
        border: 1px solid #dee2e6;
    }

    /* 打印样式 */
    @media print {
        /* 隐藏不需要打印的元素 */
        .no-print,
        .btn,
        .dropdown,
        .week-selector,
        .alert,
        .modal,
        .loading-overlay,
        .card-header .dropdown,
        .d-sm-flex,
        .save-status {
            display: none !important;
        }

        /* A4横向布局 */
        @page {
            size: A4 landscape;
            margin: 1cm;
        }

        body {
            font-size: 12px;
            line-height: 1.3;
            color: #000;
            background: white;
        }

        .container-fluid {
            max-width: none;
            padding: 0;
        }

        /* 打印标题 */
        .print-header {
            display: block !important;
            text-align: center;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .print-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .print-subtitle {
            font-size: 14px;
            margin-bottom: 5px;
        }

        /* 菜单表格打印样式 */
        .menu-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            page-break-inside: avoid;
        }

        .menu-table th,
        .menu-table td {
            border: 2px solid #000;
            padding: 8px;
            text-align: left;
            vertical-align: top;
            font-size: 11px;
            line-height: 1.4;
        }

        .menu-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
            font-size: 12px;
        }

        /* 日期列样式 */
        .menu-table td:first-child {
            width: 12%;
            text-align: center;
            font-weight: bold;
            background-color: #f8f9fa;
        }

        /* 餐次列样式 */
        .menu-table td:not(:first-child) {
            width: 29.33%;
            min-height: 80px;
            position: relative;
        }

        /* 菜品显示样式 */
        .menu-input {
            border: none;
            background: transparent;
            padding: 0;
            margin: 0;
            min-height: auto;
            font-size: 11px;
            line-height: 1.4;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        /* 打印时菜品项样式 */
        .recipe-item-display {
            display: inline-block;
            background-color: transparent;
            border: 1px solid #666;
            padding: 3px 6px;
            margin: 1px;
            border-radius: 3px;
            font-size: 10px;
            line-height: 1.3;
            page-break-inside: avoid;
        }

        /* 确保表格在一页内 */
        .card {
            border: none;
            box-shadow: none;
        }

        .card-body {
            padding: 0;
        }

        .table-responsive {
            overflow: visible;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div>
            {% if is_editable %}
                {% if existing_menu %}
                    <button id="saveMenuBtn" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存菜单
                    </button>
                    {% if existing_menu.status == '计划中' %}
                        <button id="publishMenuBtn" class="btn btn-success">
                            <i class="fas fa-check"></i> 发布菜单
                        </button>
                    {% else %}
                        <button id="unpublishMenuBtn" class="btn btn-warning">
                            <i class="fas fa-undo"></i> 解除发布
                        </button>
                    {% endif %}
                {% else %}
                    <!-- 只在本周和下周显示创建菜单按钮 -->
                    {% if week_start >= this_week_monday|string %}
                        <button id="createMenuBtn" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 创建菜单
                        </button>
                    {% endif %}
                {% endif %}
            {% endif %}
            {% if existing_menu %}
                <a href="{{ url_for('weekly_menu_v2.print_menu', id=existing_menu.id) }}" target="_blank" class="btn btn-info">
                    <i class="fas fa-print"></i> 打印菜单
                </a>
            {% else %}
                <button class="print-button" class="btn btn-info" title="请先创建菜单才能使用专业打印格式">
                    <i class="fas fa-print"></i> 打印菜单
                </button>
            {% endif %}
            <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-secondary">
                <i class="fas fa-list"></i> 返回列表
            </a>
        </div>
    </div>

    <!-- 打印标题（仅在打印时显示） -->
    <div class="print-header" style="display: none;">
        <div class="print-title">{{ area.name }}周菜单安排表</div>
        <div class="print-subtitle">{{ week_start }} 至 {{ week_end }}</div>
        <div class="print-subtitle">制表日期：<span id="print-date"></span></div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">{{ area.name }} 周菜单计划 ({{ week_start }} 至 {{ week_end }})</h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                    <div class="dropdown-header">菜单操作:</div>
                    <a class="dropdown-item" href="{{ url_for('weekly_menu_v2.copy', area_id=area_id) }}">
                        <i class="fas fa-copy fa-sm fa-fw mr-2 text-gray-400"></i>
                        复制历史菜单
                    </a>
                    {% if existing_menu %}
                        <a class="dropdown-item" href="{{ url_for('weekly_menu_v2.view', id=existing_menu.id) }}">
                            <i class="fas fa-eye fa-sm fa-fw mr-2 text-gray-400"></i>
                            查看菜单详情
                        </a>
                        {% if existing_menu.status == '已发布' %}
                            <a class="dropdown-item" href="{{ url_for('purchase_order.create_from_menu', weekly_menu_id=existing_menu.id) }}">
                                <i class="fas fa-shopping-cart fa-sm fa-fw mr-2 text-gray-400"></i>
                                创建采购订单
                            </a>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- 周次选择器 -->
            <div class="week-selector mb-4">
                {% for week in available_weeks %}
                    <div class="week-item {% if week.start_date == week_start %}active{% endif %} {% if not week.is_editable %}not-editable{% endif %}"
                         data-week="{{ week.start_date }}"
                         data-editable="{{ week.is_editable }}"
                         data-status="{{ week.status }}">
                        {{ week.display_text }}
                        <span class="badge badge-{{ week.status|status_class }}">{{ week.status }}</span>
                    </div>
                {% endfor %}
            </div>

            {% if not existing_menu and is_editable %}
                <!-- 创建菜单提示 -->
                <div id="createMenuPrompt" class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 当前周次没有菜单计划，请点击"创建菜单"按钮创建新的菜单计划。
                </div>
                <!-- 调试信息 -->
                <div class="alert alert-secondary small">
                    <strong>调试信息:</strong><br>
                    区域ID: {{ area_id }}<br>
                    周开始日期: {{ week_start }}<br>
                    用户ID: {{ current_user.id }}<br>
                    用户名: {{ current_user.name }}<br>
                    用户角色: {{ current_user.roles|map(attribute='name')|join(', ') }}
                </div>

                <!-- 错误信息显示区域 -->
                <div id="errorMessages" class="alert alert-danger" style="display: none;"></div>

                <!-- AJAX请求状态显示区域 -->
                <div id="ajaxStatus" class="alert alert-info" style="display: none;"></div>
            {% endif %}

            <!-- 菜单表单 -->
            <form id="menuForm" method="POST" action="{{ url_for('weekly_menu_v2.plan', area_id=area_id, week_start=week_start) }}" {% if not existing_menu %}style="display: none;"{% endif %}>
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <input type="hidden" id="area-id" name="area_id" value="{{ area_id }}">
                <input type="hidden" id="week-start" name="week_start" value="{{ week_start }}">
                <input type="hidden" id="menu-id" name="menu_id" value="{{ existing_menu.id if existing_menu else '' }}">
                <input type="hidden" id="menuData" name="menu_data" value="{{ menu_data|tojson }}">

                <div class="table-responsive">
                    <table class="menu-table">
                        <thead>
                            <tr>
                                <th width="15%">日期</th>
                                <th width="28%">早餐</th>
                                <th width="28%">午餐</th>
                                <th width="28%">晚餐</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for date, info in week_dates.items() %}
                                <tr>
                                    <td>{{ info.date }}<br>{{ info.weekday }}</td>
                                    <td>
                                        <div class="menu-input {% if not is_editable %}readonly{% endif %}"
                                             data-date="{{ date }}"
                                             data-meal="早餐">
                                            {% if menu_data and date in menu_data and '早餐' in menu_data[date] %}
                                                {% for recipe in menu_data[date]['早餐'] %}
                                                    <div class="recipe-item-display">{{ recipe.name }}</div>
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="menu-input {% if not is_editable %}readonly{% endif %}"
                                             data-date="{{ date }}"
                                             data-meal="午餐">
                                            {% if menu_data and date in menu_data and '午餐' in menu_data[date] %}
                                                {% for recipe in menu_data[date]['午餐'] %}
                                                    <div class="recipe-item-display">{{ recipe.name }}</div>
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="menu-input {% if not is_editable %}readonly{% endif %}"
                                             data-date="{{ date }}"
                                             data-meal="晚餐">
                                            {% if menu_data and date in menu_data and '晚餐' in menu_data[date] %}
                                                {% for recipe in menu_data[date]['晚餐'] %}
                                                    <div class="recipe-item-display">{{ recipe.name }}</div>
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <div class="save-status"></div>
            </form>
        </div>
    </div>
</div>

<!-- 菜品选择模态框 -->
<div class="modal fade" id="menuModal" tabindex="-1" role="dialog" aria-labelledby="menuModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">选择菜品</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">已选菜品</h6>
                            </div>
                            <div class="card-body">
                                <div id="selectedDishes"></div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">添加自定义菜品</h6>
                            </div>
                            <div class="card-body">
                                <div class="input-group">
                                    <input type="text" id="customDishInput" class="form-control" placeholder="输入菜品名称">
                                    <div class="input-group-append">
                                        <button id="addCustomDishBtn" class="btn btn-primary" type="button">添加</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="m-0 font-weight-bold text-primary">菜品列表</h6>
                                    </div>
                                    <div class="col-md-6">
                                        <input type="text" id="recipeSearch" class="form-control" placeholder="搜索菜品">
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- 学校/系统分类标签 -->
                                <ul class="nav nav-tabs" id="recipeSourceTabs">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-toggle="tab" href="#schoolRecipes">学校菜品</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#systemRecipes">系统菜品</a>
                                    </li>
                                </ul>

                                <div class="tab-content mt-3">
                                    <!-- 学校菜品标签页 -->
                                    <div class="tab-pane fade show active" id="schoolRecipes">
                                        <!-- 学校菜品分类标签 -->
                                        <ul class="nav nav-pills mb-3" id="schoolRecipeCategories">
                                            <li class="nav-item">
                                                <a class="nav-link active" data-category="all" data-source="school" href="#">全部</a>
                                            </li>
                                            {% for category, recipes in recipes_by_category['学校菜品'].items() %}
                                                <li class="nav-item">
                                                    <a class="nav-link" data-category="{{ category }}" data-source="school" href="#">{{ category }}</a>
                                                </li>
                                            {% endfor %}
                                        </ul>

                                        <div class="row">
                                            {% for category, recipes in recipes_by_category['学校菜品'].items() %}
                                                {% for recipe in recipes %}
                                                    <div class="col-md-3 recipe-card" data-category="{{ category }}" data-source="school">
                                                        <div class="card" data-id="{{ recipe.id }}" data-name="{{ recipe.name }}">
                                                            <div class="card-body">
                                                                <span class="badge badge-primary mb-1">学校</span>
                                                                <div>{{ recipe.name }}</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            {% endfor %}
                                        </div>
                                    </div>

                                    <!-- 系统菜品标签页 -->
                                    <div class="tab-pane fade" id="systemRecipes">
                                        <!-- 系统菜品分类标签 -->
                                        <ul class="nav nav-pills mb-3" id="systemRecipeCategories">
                                            <li class="nav-item">
                                                <a class="nav-link active" data-category="all" data-source="system" href="#">全部</a>
                                            </li>
                                            {% for category, recipes in recipes_by_category['系统菜品'].items() %}
                                                <li class="nav-item">
                                                    <a class="nav-link" data-category="{{ category }}" data-source="system" href="#">{{ category }}</a>
                                                </li>
                                            {% endfor %}
                                        </ul>

                                        <div class="row">
                                            {% for category, recipes in recipes_by_category['系统菜品'].items() %}
                                                {% for recipe in recipes %}
                                                    <div class="col-md-3 recipe-card" data-category="{{ category }}" data-source="system">
                                                        <div class="card" data-id="{{ recipe.id }}" data-name="{{ recipe.name }}">
                                                            <div class="card-body">
                                                                <span class="badge badge-secondary mb-1">系统</span>
                                                                <div>{{ recipe.name }}</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" id="saveSelectionBtn" class="btn btn-primary">保存选择</button>
            </div>
        </div>
    </div>
</div>

<!-- 加载遮罩 -->
<div class="loading-overlay">
    <div class="loading-spinner"></div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/weekly_menu_v2.js') }}"></script>
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 设置打印日期
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const printDate = `${year}年${month}月${day}日`;
    $('#print-date').text(printDate);

    // 优化打印功能
    window.addEventListener('beforeprint', function() {
        // 打印前的处理
        document.title = '{{ area.name }}周菜单安排表_{{ week_start }}至{{ week_end }}';
    });

    window.addEventListener('afterprint', function() {
        // 打印后的处理
        document.title = '{{ title }}';
    });
});
</script>
{% endblock %}
