-- 步骤1：仅添加字段，不做其他操作
-- 请逐个执行以下语句

PRINT '=== 步骤1：添加字段 ===';

-- 1. 为食材表添加 area_id 字段
BEGIN TRY
    ALTER TABLE ingredients ADD area_id INT NULL;
    PRINT '✓ 食材表 area_id 字段添加成功';
END TRY
BEGIN CATCH
    IF ERROR_NUMBER() = 2705  -- 列已存在
        PRINT '- 食材表 area_id 字段已存在';
    ELSE
        PRINT '❌ 食材表 area_id 字段添加失败: ' + ERROR_MESSAGE();
END CATCH

-- 2. 为食材表添加 is_global 字段
BEGIN TRY
    ALTER TABLE ingredients ADD is_global BIT NULL;
    PRINT '✓ 食材表 is_global 字段添加成功';
END TRY
BEGIN CATCH
    IF ERROR_NUMBER() = 2705  -- 列已存在
        PRINT '- 食材表 is_global 字段已存在';
    ELSE
        PRINT '❌ 食材表 is_global 字段添加失败: ' + ERROR_MESSAGE();
END CATCH

-- 3. 为食谱表添加 area_id 字段
BEGIN TRY
    ALTER TABLE recipes ADD area_id INT NULL;
    PRINT '✓ 食谱表 area_id 字段添加成功';
END TRY
BEGIN CATCH
    IF ERROR_NUMBER() = 2705  -- 列已存在
        PRINT '- 食谱表 area_id 字段已存在';
    ELSE
        PRINT '❌ 食谱表 area_id 字段添加失败: ' + ERROR_MESSAGE();
END CATCH

-- 4. 为食谱表添加 is_global 字段
BEGIN TRY
    ALTER TABLE recipes ADD is_global BIT NULL;
    PRINT '✓ 食谱表 is_global 字段添加成功';
END TRY
BEGIN CATCH
    IF ERROR_NUMBER() = 2705  -- 列已存在
        PRINT '- 食谱表 is_global 字段已存在';
    ELSE
        PRINT '❌ 食谱表 is_global 字段添加失败: ' + ERROR_MESSAGE();
END CATCH

PRINT '';
PRINT '=== 步骤1完成，请验证字段是否添加成功 ===';

-- 验证字段是否添加成功
SELECT 
    'ingredients' as 表名,
    COLUMN_NAME as 字段名,
    DATA_TYPE as 数据类型,
    IS_NULLABLE as 可为空
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'ingredients' 
AND COLUMN_NAME IN ('area_id', 'is_global')

UNION ALL

SELECT 
    'recipes' as 表名,
    COLUMN_NAME as 字段名,
    DATA_TYPE as 数据类型,
    IS_NULLABLE as 可为空
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'recipes' 
AND COLUMN_NAME IN ('area_id', 'is_global');
