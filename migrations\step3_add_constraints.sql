-- 步骤3：添加外键约束和索引
-- 请在步骤2成功后执行

PRINT '=== 步骤3：添加外键约束和索引 ===';

-- 1. 添加食材表外键约束
BEGIN TRY
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_ingredients_area_id')
    BEGIN
        ALTER TABLE ingredients ADD CONSTRAINT FK_ingredients_area_id 
        FOREIGN KEY (area_id) REFERENCES administrative_areas(id);
        PRINT '✓ 食材表外键约束添加成功';
    END
    ELSE
    BEGIN
        PRINT '- 食材表外键约束已存在';
    END
END TRY
BEGIN CATCH
    PRINT '❌ 食材表外键约束添加失败: ' + ERROR_MESSAGE();
END CATCH

-- 2. 添加食谱表外键约束
BEGIN TRY
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_recipes_area_id')
    BEGIN
        ALTER TABLE recipes ADD CONSTRAINT FK_recipes_area_id 
        FOREIGN KEY (area_id) REFERENCES administrative_areas(id);
        PRINT '✓ 食谱表外键约束添加成功';
    END
    ELSE
    BEGIN
        PRINT '- 食谱表外键约束已存在';
    END
END TRY
BEGIN CATCH
    PRINT '❌ 食谱表外键约束添加失败: ' + ERROR_MESSAGE();
END CATCH

-- 3. 创建索引以提高查询性能
BEGIN TRY
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ingredients_area_id_is_global')
    BEGIN
        CREATE INDEX IX_ingredients_area_id_is_global ON ingredients (area_id, is_global);
        PRINT '✓ 食材表复合索引创建成功';
    END
    ELSE
    BEGIN
        PRINT '- 食材表复合索引已存在';
    END
END TRY
BEGIN CATCH
    PRINT '❌ 食材表索引创建失败: ' + ERROR_MESSAGE();
END CATCH

BEGIN TRY
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_recipes_area_id_is_global')
    BEGIN
        CREATE INDEX IX_recipes_area_id_is_global ON recipes (area_id, is_global);
        PRINT '✓ 食谱表复合索引创建成功';
    END
    ELSE
    BEGIN
        PRINT '- 食谱表复合索引已存在';
    END
END TRY
BEGIN CATCH
    PRINT '❌ 食谱表索引创建失败: ' + ERROR_MESSAGE();
END CATCH

-- 4. 添加字段注释
BEGIN TRY
    EXEC sp_addextendedproperty 
        @name = N'MS_Description', 
        @value = N'学校区域ID，用于数据隔离。NULL表示全局数据', 
        @level0type = N'SCHEMA', @level0name = N'dbo',
        @level1type = N'TABLE', @level1name = N'ingredients',
        @level2type = N'COLUMN', @level2name = N'area_id';
    PRINT '✓ 食材表 area_id 字段注释添加成功';
END TRY
BEGIN CATCH
    PRINT '- 食材表 area_id 字段注释可能已存在';
END CATCH

BEGIN TRY
    EXEC sp_addextendedproperty 
        @name = N'MS_Description', 
        @value = N'是否为全局食材（系统预设）。1=全局，0=学校专用', 
        @level0type = N'SCHEMA', @level0name = N'dbo',
        @level1type = N'TABLE', @level1name = N'ingredients',
        @level2type = N'COLUMN', @level2name = N'is_global';
    PRINT '✓ 食材表 is_global 字段注释添加成功';
END TRY
BEGIN CATCH
    PRINT '- 食材表 is_global 字段注释可能已存在';
END CATCH

BEGIN TRY
    EXEC sp_addextendedproperty 
        @name = N'MS_Description', 
        @value = N'学校区域ID，用于数据隔离。NULL表示全局数据', 
        @level0type = N'SCHEMA', @level0name = N'dbo',
        @level1type = N'TABLE', @level1name = N'recipes',
        @level2type = N'COLUMN', @level2name = N'area_id';
    PRINT '✓ 食谱表 area_id 字段注释添加成功';
END TRY
BEGIN CATCH
    PRINT '- 食谱表 area_id 字段注释可能已存在';
END CATCH

BEGIN TRY
    EXEC sp_addextendedproperty 
        @name = N'MS_Description', 
        @value = N'是否为全局食谱（系统预设）。1=全局，0=学校专用', 
        @level0type = N'SCHEMA', @level0name = N'dbo',
        @level1type = N'TABLE', @level1name = N'recipes',
        @level2type = N'COLUMN', @level2name = N'is_global';
    PRINT '✓ 食谱表 is_global 字段注释添加成功';
END TRY
BEGIN CATCH
    PRINT '- 食谱表 is_global 字段注释可能已存在';
END CATCH

PRINT '';
PRINT '=== 步骤3完成 ===';
PRINT '=== 学校级隔离迁移全部完成！ ===';

-- 最终验证
SELECT 
    '最终验证' as 状态,
    '食材表字段数' as 项目,
    COUNT(*) as 值
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'ingredients' 
AND COLUMN_NAME IN ('area_id', 'is_global')

UNION ALL

SELECT 
    '最终验证' as 状态,
    '食谱表字段数' as 项目,
    COUNT(*) as 值
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'recipes' 
AND COLUMN_NAME IN ('area_id', 'is_global')

UNION ALL

SELECT 
    '最终验证' as 状态,
    '外键约束数' as 项目,
    COUNT(*) as 值
FROM sys.foreign_keys 
WHERE name IN ('FK_ingredients_area_id', 'FK_recipes_area_id')

UNION ALL

SELECT 
    '最终验证' as 状态,
    '索引数' as 项目,
    COUNT(*) as 值
FROM sys.indexes 
WHERE name IN ('IX_ingredients_area_id_is_global', 'IX_recipes_area_id_is_global');
