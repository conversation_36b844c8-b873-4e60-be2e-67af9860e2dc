-- 快速修复：为食材和食谱添加学校级隔离字段
-- 简化版本，用于立即执行

PRINT '开始快速修复学校级隔离字段...';

-- 1. 为食材表添加字段
BEGIN TRY
    -- 添加 area_id 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ingredients') AND name = 'area_id')
    BEGIN
        ALTER TABLE ingredients ADD area_id INT NULL;
        PRINT '✓ 食材表添加 area_id 字段成功';
    END
    ELSE
    BEGIN
        PRINT '- 食材表 area_id 字段已存在';
    END

    -- 添加 is_global 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ingredients') AND name = 'is_global')
    BEGIN
        ALTER TABLE ingredients ADD is_global BIT NOT NULL DEFAULT 0;
        PRINT '✓ 食材表添加 is_global 字段成功';
    END
    ELSE
    BEGIN
        PRINT '- 食材表 is_global 字段已存在';
    END
END TRY
BEGIN CATCH
    PRINT '❌ 食材表字段添加失败: ' + ERROR_MESSAGE();
END CATCH

-- 2. 为食谱表添加字段
BEGIN TRY
    -- 添加 area_id 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('recipes') AND name = 'area_id')
    BEGIN
        ALTER TABLE recipes ADD area_id INT NULL;
        PRINT '✓ 食谱表添加 area_id 字段成功';
    END
    ELSE
    BEGIN
        PRINT '- 食谱表 area_id 字段已存在';
    END

    -- 添加 is_global 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('recipes') AND name = 'is_global')
    BEGIN
        ALTER TABLE recipes ADD is_global BIT NOT NULL DEFAULT 0;
        PRINT '✓ 食谱表添加 is_global 字段成功';
    END
    ELSE
    BEGIN
        PRINT '- 食谱表 is_global 字段已存在';
    END
END TRY
BEGIN CATCH
    PRINT '❌ 食谱表字段添加失败: ' + ERROR_MESSAGE();
END CATCH

-- 3. 添加外键约束
BEGIN TRY
    -- 食材表外键
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_ingredients_area_id')
    BEGIN
        ALTER TABLE ingredients ADD CONSTRAINT FK_ingredients_area_id 
        FOREIGN KEY (area_id) REFERENCES administrative_areas(id);
        PRINT '✓ 食材表外键约束添加成功';
    END
    ELSE
    BEGIN
        PRINT '- 食材表外键约束已存在';
    END

    -- 食谱表外键
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_recipes_area_id')
    BEGIN
        ALTER TABLE recipes ADD CONSTRAINT FK_recipes_area_id 
        FOREIGN KEY (area_id) REFERENCES administrative_areas(id);
        PRINT '✓ 食谱表外键约束添加成功';
    END
    ELSE
    BEGIN
        PRINT '- 食谱表外键约束已存在';
    END
END TRY
BEGIN CATCH
    PRINT '❌ 外键约束添加失败: ' + ERROR_MESSAGE();
END CATCH

-- 4. 更新现有数据为全局数据
BEGIN TRY
    -- 更新食材
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ingredients') AND name = 'is_global')
    BEGIN
        UPDATE ingredients SET is_global = 1 WHERE area_id IS NULL;
        PRINT '✓ 现有食材已标记为全局数据';
    END

    -- 更新食谱
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('recipes') AND name = 'is_global')
    BEGIN
        UPDATE recipes SET is_global = 1 WHERE area_id IS NULL;
        PRINT '✓ 现有食谱已标记为全局数据';
    END
END TRY
BEGIN CATCH
    PRINT '❌ 数据更新失败: ' + ERROR_MESSAGE();
END CATCH

-- 5. 创建索引
BEGIN TRY
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ingredients_area_id_is_global')
    BEGIN
        CREATE INDEX IX_ingredients_area_id_is_global ON ingredients (area_id, is_global);
        PRINT '✓ 食材表索引创建成功';
    END

    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_recipes_area_id_is_global')
    BEGIN
        CREATE INDEX IX_recipes_area_id_is_global ON recipes (area_id, is_global);
        PRINT '✓ 食谱表索引创建成功';
    END
END TRY
BEGIN CATCH
    PRINT '❌ 索引创建失败: ' + ERROR_MESSAGE();
END CATCH

-- 6. 验证结果
PRINT '';
PRINT '=== 验证结果 ===';

-- 检查字段是否存在
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ingredients') AND name = 'area_id')
   AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ingredients') AND name = 'is_global')
   AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('recipes') AND name = 'area_id')
   AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('recipes') AND name = 'is_global')
BEGIN
    PRINT '✓ 所有字段添加成功';
    
    -- 显示统计信息
    SELECT 
        '食材统计' as 类型,
        COUNT(*) as 总数,
        SUM(CASE WHEN is_global = 1 THEN 1 ELSE 0 END) as 全局数量,
        SUM(CASE WHEN area_id IS NOT NULL THEN 1 ELSE 0 END) as 学校专用数量
    FROM ingredients

    UNION ALL

    SELECT 
        '食谱统计' as 类型,
        COUNT(*) as 总数,
        SUM(CASE WHEN is_global = 1 THEN 1 ELSE 0 END) as 全局数量,
        SUM(CASE WHEN area_id IS NOT NULL THEN 1 ELSE 0 END) as 学校专用数量
    FROM recipes;
END
ELSE
BEGIN
    PRINT '❌ 部分字段添加失败，请检查错误信息';
END

PRINT '';
PRINT '=== 快速修复完成 ===';
