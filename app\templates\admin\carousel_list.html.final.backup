{% extends "base.html" %}

{% block title %}轮播图管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-images me-2"></i>首页轮播图管理
                    </h3>
                    <div class="btn-group">
                        <a href="{{ url_for('homepage_carousel.admin_create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>添加轮播图
                        </a>
                        <a href="{{ url_for('homepage_carousel.admin_batch_upload') }}" class="btn btn-success">
                            <i class="fas fa-upload me-1"></i>批量上传
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    {% if carousels %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th width="80">预览</th>
                                    <th>标题</th>
                                    <th>描述</th>
                                    <th width="80">排序</th>
                                    <th width="80">状态</th>
                                    <th width="120">创建时间</th>
                                    <th width="150">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for carousel in carousels %}
                                <tr>
                                    <td>
                                        <img src="{{ carousel.image_path }}"
                                             alt="{{ carousel.title }}"
                                             class="img-thumbnail"
                                             style="width: 60px; height: 40px; object-fit: cover;">
                                    </td>
                                    <td>
                                        <strong>{{ carousel.title }}</strong>
                                        {% if carousel.link_url %}
                                        <br><small class="text-muted">
                                            <i class="fas fa-link"></i>
                                            <a href="{{ carousel.link_url }}" target="_blank" class="text-decoration-none">
                                                {{ carousel.link_url[:50] }}{% if carousel.link_url|length > 50 %}...{% endif %}
                                            </a>
                                        </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if carousel.description %}
                                            {{ carousel.description[:100] }}{% if carousel.description|length > 100 %}...{% endif %}
                                        {% else %}
                                            <span class="text-muted">无描述</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ carousel.sort_order }}</span>
                                    </td>
                                    <td>
                                        {% if carousel.is_active %}
                                            <span class="badge bg-success">启用</span>
                                        {% else %}
                                            <span class="badge bg-secondary">禁用</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ carousel.created_at }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('homepage_carousel.admin_edit', id=carousel.id) }}"
                                               class="btn btn-outline-primary" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>

                                            <button type="button"
                                                    class="btn btn-outline-{% if carousel.is_active %}warning{% else %}success{% endif %}"
                                                    onclick="toggleStatus({{ carousel.id }})"
                                                    title="{% if carousel.is_active %}禁用{% else %}启用{% endif %}">
                                                <i class="fas fa-{% if carousel.is_active %}eye-slash{% else %}eye{% endif %}"></i>
                                            </button>

                                            <button type="button"
                                                    class="btn btn-outline-danger"
                                                    data-action="critical-confirm" data-original-onclick="deleteCarousel({{ carousel.id }}, " style="cursor: pointer;"
                                                    title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-images fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无轮播图</h5>
                        <p class="text-muted">点击上方"添加轮播图"按钮开始创建</p>
                        <div class="btn-group">
                            <a href="{{ url_for('homepage_carousel.admin_create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>添加第一个轮播图
                            </a>
                            <a href="{{ url_for('homepage_carousel.admin_batch_upload') }}" class="btn btn-success">
                                <i class="fas fa-upload me-1"></i>批量上传
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除轮播图 "<span id="deleteTitle"></span>" 吗？</p>
                <p class="text-danger"><small>此操作不可撤销，图片文件也会被删除。</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
function toggleStatus(id) {
    fetch(`/admin/carousel/${id}/toggle`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('操作失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('操作失败');
    });
}

function deleteCarousel(id, title) {
    document.getElementById('deleteTitle').textContent = title;
    document.getElementById('deleteForm').action = `/admin/carousel/${id}/delete`;

    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// 图片预览功能
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('.img-thumbnail');
    images.forEach(img => {
        img.addEventListener('click', function() {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${this.alt}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center">
                            <img src="${this.src}" class="img-fluid" alt="${this.alt}">
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        });
    });
});
</script>
{% endblock %}
