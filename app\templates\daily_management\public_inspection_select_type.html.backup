<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}">
    <style nonce="{{ csp_nonce }}">
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .main-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0 !important;
            padding: 30px;
            text-align: center;
            border: none;
        }

        .school-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .type-selection-container {
            padding: 40px;
        }

        .inspection-type-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            background: white;
        }

        .inspection-type-card:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
        }

        .inspection-type-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .type-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .type-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .type-description {
            font-size: 0.95rem;
            opacity: 0.8;
        }

        .btn-continue {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-continue:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-continue:disabled {
            background: #6c757d;
            transform: none;
            box-shadow: none;
        }

        .btn-back {
            border: 2px solid #6c757d;
            color: #6c757d;
            border-radius: 15px;
            padding: 15px 30px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .btn-back:hover {
            background: #6c757d;
            color: white;
        }

        .action-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .date-badge {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-md-10">
                    <div class="card">
                        <!-- 卡片头部 -->
                        <div class="card-header">
                            <div class="school-icon">
                                <i class="fas fa-school"></i>
                            </div>
                            <h2 class="mb-2">{{ school.name }}</h2>
                            <div>
                                <span class="action-badge">{{ action_name }}</span>
                                <span class="date-badge">{{ selected_date.strftime('%Y年%m月%d日') }}</span>
                            </div>
                        </div>

                        <!-- 卡片内容 -->
                        <div class="type-selection-container">
                            <div class="text-center mb-4">
                                <h4 class="text-dark mb-3">
                                    <i class="fas fa-clock mr-2"></i>选择检查时间
                                </h4>
                                <p class="text-muted">
                                    请选择要进行{{ action_name }}的检查时间段
                                </p>
                            </div>

                            <!-- 检查类型选择 -->
                            <div class="row">
                                <!-- 晨检 -->
                                <div class="col-md-4 mb-3">
                                    <div class="inspection-type-card text-center" data-type="morning">
                                        <div class="type-icon">
                                            <i class="fas fa-sun text-warning"></i>
                                        </div>
                                        <div class="type-title">晨检</div>
                                        <div class="type-description">
                                            早晨检查<br>
                                            (06:00-10:00)
                                        </div>
                                    </div>
                                </div>

                                <!-- 午检 -->
                                <div class="col-md-4 mb-3">
                                    <div class="inspection-type-card text-center" data-type="noon">
                                        <div class="type-icon">
                                            <i class="fas fa-sun text-primary"></i>
                                        </div>
                                        <div class="type-title">午检</div>
                                        <div class="type-description">
                                            中午检查<br>
                                            (11:00-14:00)
                                        </div>
                                    </div>
                                </div>

                                <!-- 晚检 -->
                                <div class="col-md-4 mb-3">
                                    <div class="inspection-type-card text-center" data-type="evening">
                                        <div class="type-icon">
                                            <i class="fas fa-moon text-info"></i>
                                        </div>
                                        <div class="type-title">晚检</div>
                                        <div class="type-description">
                                            晚间检查<br>
                                            (17:00-20:00)
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="text-center mt-4">
                                <button type="button" class="btn btn-back me-3" onclick="goBack()">
                                    <i class="fas fa-arrow-left mr-2"></i>返回
                                </button>
                                <button type="button" class="btn btn-primary btn-continue" id="continueBtn" disabled>
                                    <i class="fas fa-arrow-right mr-2"></i>继续
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
    <!-- Bootstrap JS -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <script nonce="{{ csp_nonce }}">
        let selectedType = null;

        // 检查类型卡片点击事件
        document.querySelectorAll('.inspection-type-card').forEach(card => {
            card.addEventListener('click', function() {
                // 移除所有选中状态
                document.querySelectorAll('.inspection-type-card').forEach(c => {
                    c.classList.remove('selected');
                });

                // 添加选中状态
                this.classList.add('selected');
                selectedType = this.dataset.type;

                // 启用继续按钮
                document.getElementById('continueBtn').disabled = false;
            });
        });

        // 继续按钮点击事件
        document.getElementById('continueBtn').addEventListener('click', function() {
            if (!selectedType) {
                alert('请选择检查时间');
                return;
            }

            // 根据操作类型跳转到相应页面
            let url;
            if ('{{ action }}' === 'upload') {
                url = `/daily-management/public/inspections/upload/{{ school.id }}/{{ log.id }}/${selectedType}`;
            } else if ('{{ action }}' === 'rate') {
                url = `/daily-management/public/inspections/rate/{{ school.id }}/{{ log.id }}/${selectedType}`;
            }

            if (url) {
                window.location.href = url;
            }
        });

        // 返回按钮
        function goBack() {
            window.history.back();
        }

        // 键盘事件
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && selectedType) {
                document.getElementById('continueBtn').click();
            } else if (e.key === 'Escape') {
                goBack();
            }
        });
    </script>
</body>
</html>
