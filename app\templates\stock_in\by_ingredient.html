{% extends 'base.html' %}

{% block title %}食材入库历史 - {{ ingredient.name if ingredient else '-' }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">食材入库历史 - {{ ingredient.name if ingredient else '-' }}</h3>
                    <div class="card-tools">
                         {# Optional: Link back to ingredient list or other relevant page #}
                        {# <a href="{{ url_for('ingredient.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回食材列表
                        </a> #}
                    </div>
                </div>
                <div class="card-body">

                    {# Ingredient Information #}
                    <div class="card mb-3">
                        <div class="card-header bg-info">
                            <h4 class="card-title">食材信息</h4>
                        </div>
                        <div class="card-body">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">食材名称</th>
                                    <td>{{ ingredient.name if ingredient else '-' }}</td>
                                </tr>
                                <tr>
                                    <th>食材编码</th>
                                    <td>{{ ingredient.code if ingredient else '-' }}</td>
                                </tr>
                                {% if ingredient and ingredient.category %}
                                <tr>
                                    <th>分类</th>
                                    <td>{{ ingredient.category.name }}</td>
                                </tr>
                                {% endif %}
                                {% if ingredient and ingredient.unit %}
                                <tr>
                                    <th>单位</th>
                                    <td>{{ ingredient.unit }}</td>
                                </tr>
                                {% endif %}
                                {% if ingredient and ingredient.notes %}
                                <tr>
                                    <th>备注</th>
                                    <td>{{ ingredient.notes }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    {# Stock In Items List for this Ingredient #}
                    <div class="card mt-4">
                        <div class="card-header bg-success">
                            <h4 class="card-title">入库记录列表</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>入库单号</th>
                                            <th>入库日期</th>
                                            <th>批次号</th>
                                            <th>入库数量</th>
                                            <th>单位</th>
                                            <th>供应商</th>
                                            <th>备注</th>
                                            {# Optional: Add links to StockIn view or StockInItem detail #}
                                            {# <th>操作</th> #}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in stock_in_items %}
                                        <tr>
                                            <td>
                                                 {# Link to the parent StockIn detail page #}
                                                <a href="{{ url_for('stock_in.view', id=item.stock_in.id) }}">
                                                     {{ item.stock_in.stock_in_number if item.stock_in else '-' }}
                                                </a>
                                            </td>
                                            <td>{{ item.stock_in.stock_in_date|format_datetime('%Y-%m-%d') if item.stock_in and item.stock_in.stock_in_date else '-' }}</td>
                                            <td>{{ item.batch_number }}</td>
                                            <td>{{ item.quantity }}</td>
                                            <td>{{ item.unit }}</td>
                                            <td>{{ item.stock_in.supplier.name if item.stock_in and item.stock_in.supplier else '-' }}</td>
                                            <td>{{ item.notes or '-' }}</td>
                                            {# Optional: Add links #}
                                            {#
                                            <td>
                                                <a href="{{ url_for('stock_in_item.detail', item_id=item.id) }}" class="btn btn-info btn-sm">详情</a>
                                            </td>
                                            #}
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="7" class="text-center">没有找到该食材的入库记录。</td> {# Adjust colspan based on number of columns #}
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                </div> {# card-body #}
            </div> {# card #}
        </div> {# col #}
    </div> {# row #}
</div> {# container-fluid #}
{% endblock %} 