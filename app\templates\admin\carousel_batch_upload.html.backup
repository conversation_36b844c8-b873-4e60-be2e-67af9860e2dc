{% extends "base.html" %}

{% block title %}批量上传轮播图{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-upload me-2"></i>批量上传轮播图
                    </h3>
                </div>

                <form method="POST" enctype="multipart/form-data" id="batchUploadForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <div class="card-body">
                        <div class="row">
                            <!-- 左侧：基本信息 -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="default_description" class="form-label">默认描述</label>
                                    <textarea class="form-control"
                                              id="default_description"
                                              name="default_description"
                                              rows="3"
                                              placeholder="为所有轮播图设置统一的描述信息">{{ request.form.get('default_description', '') }}</textarea>
                                    <div class="form-text">可选，将应用到所有上传的图片</div>
                                </div>

                                <div class="mb-3">
                                    <label for="default_link_url" class="form-label">默认链接</label>
                                    <input type="text"
                                           class="form-control"
                                           id="default_link_url"
                                           name="default_link_url"
                                           value="/"
                                           placeholder="/">
                                    <div class="form-text">
                                        点击图片时跳转的链接<br>
                                        <small class="text-muted">默认跳转到首页 (/)，将应用到所有图片</small>
                                    </div>
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="setDefaultLink('/')">
                                            <i class="fas fa-home me-1"></i>首页
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setDefaultLink('/auth/login')">
                                            <i class="fas fa-sign-in-alt me-1"></i>登录
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setDefaultLink('/auth/register')">
                                            <i class="fas fa-user-plus me-1"></i>注册
                                        </button>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input"
                                               type="checkbox"
                                               id="is_active"
                                               name="is_active"
                                               value="1"
                                               checked>
                                        <label class="form-check-label" for="is_active">
                                            启用所有轮播图
                                        </label>
                                    </div>
                                    <div class="form-text">禁用的轮播图不会在首页显示</div>
                                </div>
                            </div>

                            <!-- 右侧：文件上传 -->
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="images" class="form-label">
                                        选择轮播图片 <span class="text-danger">*</span>
                                    </label>
                                    <input type="file"
                                           class="form-control"
                                           id="images"
                                           name="images"
                                           accept="image/*"
                                           multiple
                                           required>
                                    <div class="form-text">
                                        <strong>支持格式：</strong>PNG、JPG、JPEG、GIF、WebP<br>
                                        <strong>建议尺寸：</strong>1200x600像素<br>
                                        <strong>文件大小：</strong>每张不超过5MB<br>
                                        <strong>可以选择多张图片一次性上传</strong>
                                    </div>
                                </div>

                                <!-- 拖拽上传区域 -->
                                <div class="upload-drop-zone mb-3" id="dropZone">
                                    <div class="text-center py-5">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">拖拽图片到这里</h5>
                                        <p class="text-muted">或点击上方"选择文件"按钮</p>
                                    </div>
                                </div>

                                <!-- 图片预览区域 -->
                                <div id="previewContainer" style="display: none;">
                                    <label class="form-label">图片预览和标题设置</label>
                                    <div id="previewList" class="border rounded p-3 bg-light">
                                        <!-- 动态生成预览图片 -->
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle"></i>
                                            每张图片都会创建为独立的轮播图，可以为每张图片设置不同的标题
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('homepage_carousel.admin_list') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>返回列表
                            </a>
                            <div>
                                <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                                    <i class="fas fa-undo me-1"></i>重置
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-upload me-1"></i>批量上传
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style nonce="{{ csp_nonce }}">
.upload-drop-zone {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-drop-zone:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.upload-drop-zone.dragover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.preview-item {
    transition: all 0.3s ease;
}

.preview-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.preview-image {
    max-height: 120px;
    object-fit: cover;
    border-radius: 4px;
}
</style>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
// 页面加载时确保默认值
document.addEventListener('DOMContentLoaded', function() {
    const defaultLinkInput = document.getElementById('default_link_url');

    // 如果输入框为空，设置默认值
    if (!defaultLinkInput.value || defaultLinkInput.value.trim() === '') {
        defaultLinkInput.value = '/';
    }

    // 高亮首页按钮
    highlightActiveButton('/');
});

// 设置默认链接
function setDefaultLink(url) {
    const input = document.getElementById('default_link_url');
    input.value = url;

    // 高亮当前选中的按钮
    highlightActiveButton(url);

    // 添加视觉反馈
    input.focus();
    input.select();
}

// 高亮活跃按钮
function highlightActiveButton(url) {
    // 移除所有按钮的高亮
    document.querySelectorAll('.btn-outline-primary, .btn-outline-secondary').forEach(btn => {
        if (btn.onclick && btn.onclick.toString().includes('setDefaultLink')) {
            btn.className = btn.className.replace('btn-outline-primary', 'btn-outline-secondary');
        }
    });

    // 高亮匹配的按钮
    document.querySelectorAll('button[onclick*="setDefaultLink"]').forEach(btn => {
        const btnUrl = btn.onclick.toString().match(/setDefaultLink\('([^']+)'\)/);
        if (btnUrl && btnUrl[1] === url) {
            btn.className = btn.className.replace('btn-outline-secondary', 'btn-outline-primary');
        }
    });
}

// 多图片预览功能
document.getElementById('images').addEventListener('change', function(e) {
    handleFiles(e.target.files);
});

// 拖拽上传功能
const dropZone = document.getElementById('dropZone');
const fileInput = document.getElementById('images');

dropZone.addEventListener('click', () => fileInput.click());

dropZone.addEventListener('dragover', (e) => {
    e.preventDefault();
    dropZone.classList.add('dragover');
});

dropZone.addEventListener('dragleave', () => {
    dropZone.classList.remove('dragover');
});

dropZone.addEventListener('drop', (e) => {
    e.preventDefault();
    dropZone.classList.remove('dragover');

    const files = e.dataTransfer.files;
    fileInput.files = files;
    handleFiles(files);
});

function handleFiles(files) {
    const previewContainer = document.getElementById('previewContainer');
    const previewList = document.getElementById('previewList');

    // 清空之前的预览
    previewList.innerHTML = '';

    if (files.length > 0) {
        let validFiles = 0;
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
        const maxSize = 5 * 1024 * 1024; // 5MB

        Array.from(files).forEach((file, index) => {
            // 检查文件类型
            if (!allowedTypes.includes(file.type)) {
                alert(`文件 "${file.name}" 不支持，请选择 PNG、JPG、JPEG、GIF 或 WebP 格式的图片`);
                return;
            }

            // 检查文件大小
            if (file.size > maxSize) {
                alert(`文件 "${file.name}" 大小超过5MB限制`);
                return;
            }

            validFiles++;

            // 创建预览容器
            const previewItem = document.createElement('div');
            previewItem.className = 'preview-item mb-3 p-3 border rounded bg-white';

            // 创建图片预览
            const reader = new FileReader();
            reader.onload = function(e) {
                previewItem.innerHTML = `
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <img src="${e.target.result}" class="img-fluid preview-image" alt="预览 ${index + 1}">
                        </div>
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-8">
                                    <label class="form-label small">图片标题 ${index + 1}</label>
                                    <input type="text" class="form-control form-control-sm"
                                           placeholder="轮播图 ${index + 1}"
                                           name="image_titles[]"
                                           value="轮播图 ${index + 1}">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label small">文件信息</label>
                                    <p class="mb-0 text-muted small">${file.name}</p>
                                    <p class="mb-0 text-muted small">大小: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                previewList.appendChild(previewItem);
            };
            reader.readAsDataURL(file);
        });

        if (validFiles > 0) {
            previewContainer.style.display = 'block';

            // 显示提示信息
            setTimeout(() => {
                const infoDiv = document.createElement('div');
                infoDiv.className = 'alert alert-info mt-3';
                infoDiv.innerHTML = `
                    <i class="fas fa-info-circle"></i>
                    已选择 ${validFiles} 张图片，将创建 ${validFiles} 个轮播图项目
                `;
                previewList.appendChild(infoDiv);
            }, 100);
        } else {
            previewContainer.style.display = 'none';
        }
    } else {
        previewContainer.style.display = 'none';
    }
}

// 重置表单
function resetForm() {
    if (confirm('确定要重置表单吗？所有未保存的更改将丢失。')) {
        document.getElementById('batchUploadForm').reset();
        document.getElementById('previewContainer').style.display = 'none';

        // 确保默认值设置
        document.getElementById('default_link_url').value = '/';
        document.getElementById('is_active').checked = true;

        // 重新高亮首页按钮
        highlightActiveButton('/');

        // 显示重置成功提示
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>表单已重置，默认链接已设置为首页 (/)
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const cardBody = document.querySelector('.card-body');
        cardBody.insertBefore(alertDiv, cardBody.firstChild);

        // 3秒后自动隐藏提示
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }
}

// 表单验证
document.getElementById('batchUploadForm').addEventListener('submit', function(e) {
    const images = document.getElementById('images').files;

    if (!images || images.length === 0) {
        alert('请选择要上传的图片文件');
        e.preventDefault();
        return;
    }

    // 显示加载状态
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>上传中...';
    submitBtn.disabled = true;

    // 如果验证失败，恢复按钮状态
    setTimeout(() => {
        if (submitBtn.disabled) {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }, 30000); // 批量上传可能需要更长时间
});
</script>
{% endblock %}
