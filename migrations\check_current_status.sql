-- 检查当前数据库状态
PRINT '=== 检查当前数据库状态 ===';

-- 1. 检查食材表字段
PRINT '1. 食材表字段检查：';
SELECT 
    COLUMN_NAME as 字段名,
    DATA_TYPE as 数据类型,
    IS_NULLABLE as 可为空,
    COLUMN_DEFAULT as 默认值
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'ingredients' 
AND COLUMN_NAME IN ('area_id', 'is_global')
ORDER BY COLUMN_NAME;

-- 2. 检查食谱表字段
PRINT '2. 食谱表字段检查：';
SELECT 
    COLUMN_NAME as 字段名,
    DATA_TYPE as 数据类型,
    IS_NULLABLE as 可为空,
    COLUMN_DEFAULT as 默认值
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'recipes' 
AND COLUMN_NAME IN ('area_id', 'is_global')
ORDER BY COLUMN_NAME;

-- 3. 检查外键约束
PRINT '3. 外键约束检查：';
SELECT 
    fk.name as 约束名,
    t.name as 表名,
    c.name as 字段名,
    rt.name as 引用表,
    rc.name as 引用字段
FROM sys.foreign_keys fk
INNER JOIN sys.tables t ON fk.parent_object_id = t.object_id
INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
INNER JOIN sys.columns c ON fkc.parent_object_id = c.object_id AND fkc.parent_column_id = c.column_id
INNER JOIN sys.tables rt ON fkc.referenced_object_id = rt.object_id
INNER JOIN sys.columns rc ON fkc.referenced_object_id = rc.object_id AND fkc.referenced_column_id = rc.column_id
WHERE fk.name IN ('FK_ingredients_area_id', 'FK_recipes_area_id')
ORDER BY fk.name;

-- 4. 检查索引
PRINT '4. 索引检查：';
SELECT 
    i.name as 索引名,
    t.name as 表名,
    i.type_desc as 索引类型
FROM sys.indexes i
INNER JOIN sys.tables t ON i.object_id = t.object_id
WHERE i.name IN ('IX_ingredients_area_id_is_global', 'IX_recipes_area_id_is_global')
ORDER BY i.name;

-- 5. 检查数据分布
PRINT '5. 数据分布检查：';

-- 检查食材表是否有 is_global 字段
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ingredients') AND name = 'is_global')
BEGIN
    SELECT 
        '食材表' as 表名,
        COUNT(*) as 总数,
        SUM(CASE WHEN area_id IS NULL THEN 1 ELSE 0 END) as area_id为空,
        SUM(CASE WHEN area_id IS NOT NULL THEN 1 ELSE 0 END) as area_id不为空,
        SUM(CASE WHEN is_global = 1 THEN 1 ELSE 0 END) as 全局标记,
        SUM(CASE WHEN is_global = 0 THEN 1 ELSE 0 END) as 非全局标记,
        SUM(CASE WHEN is_global IS NULL THEN 1 ELSE 0 END) as is_global为空
    FROM ingredients;
END
ELSE
BEGIN
    SELECT 
        '食材表' as 表名,
        COUNT(*) as 总数,
        SUM(CASE WHEN area_id IS NULL THEN 1 ELSE 0 END) as area_id为空,
        SUM(CASE WHEN area_id IS NOT NULL THEN 1 ELSE 0 END) as area_id不为空,
        0 as 全局标记,
        0 as 非全局标记,
        COUNT(*) as is_global为空
    FROM ingredients;
END

-- 检查食谱表是否有 is_global 字段
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('recipes') AND name = 'is_global')
BEGIN
    SELECT 
        '食谱表' as 表名,
        COUNT(*) as 总数,
        SUM(CASE WHEN area_id IS NULL THEN 1 ELSE 0 END) as area_id为空,
        SUM(CASE WHEN area_id IS NOT NULL THEN 1 ELSE 0 END) as area_id不为空,
        SUM(CASE WHEN is_global = 1 THEN 1 ELSE 0 END) as 全局标记,
        SUM(CASE WHEN is_global = 0 THEN 1 ELSE 0 END) as 非全局标记,
        SUM(CASE WHEN is_global IS NULL THEN 1 ELSE 0 END) as is_global为空
    FROM recipes;
END
ELSE
BEGIN
    SELECT 
        '食谱表' as 表名,
        COUNT(*) as 总数,
        SUM(CASE WHEN area_id IS NULL THEN 1 ELSE 0 END) as area_id为空,
        SUM(CASE WHEN area_id IS NOT NULL THEN 1 ELSE 0 END) as area_id不为空,
        0 as 全局标记,
        0 as 非全局标记,
        COUNT(*) as is_global为空
    FROM recipes;
END

PRINT '=== 状态检查完成 ===';
