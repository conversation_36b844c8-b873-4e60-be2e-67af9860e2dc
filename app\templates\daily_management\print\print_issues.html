{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/print-styles.css') }}">
<style nonce="{{ csp_nonce }}">
    body {
        font-size: 14pt;
    }
    
    .section-title {
        font-size: 16pt;
        font-weight: bold;
        margin-top: 20px;
        margin-bottom: 10px;
        border-bottom: 1px solid #4e73df;
        padding-bottom: 5px;
        color: #4e73df;
    }
    
    .info-row {
        margin-bottom: 10px;
    }
    
    .info-label {
        font-weight: bold;
    }
    
    .print-page {
        padding: 20mm;
        margin-bottom: 20mm;
        border: 1px solid #ddd;
        background: #fff;
    }
    
    .badge-pending {
        background-color: #f6c23e;
        color: #fff;
        padding: 3px 8px;
        border-radius: 10px;
    }
    
    .badge-resolved {
        background-color: #1cc88a;
        color: #fff;
        padding: 3px 8px;
        border-radius: 10px;
    }
    
    .badge-high {
        background-color: #e74a3b;
        color: #fff;
        padding: 3px 8px;
        border-radius: 10px;
    }
    
    .badge-medium {
        background-color: #f6c23e;
        color: #fff;
        padding: 3px 8px;
        border-radius: 10px;
    }
    
    .badge-low {
        background-color: #36b9cc;
        color: #fff;
        padding: 3px 8px;
        border-radius: 10px;
    }
    
    @media print {
        .no-print {
            display: none !important;
        }
        
        body {
            font-size: 12pt;
        }
        
        .section-title {
            font-size: 14pt;
        }
        
        .print-page {
            padding: 0;
            margin: 0;
            border: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 打印控制按钮 -->
    <div class="no-print mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">打印预览 - 问题记录</h6>
                <div>
                    <a href="{{ url_for('daily_management.issues', log_id=log.id) }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left mr-1"></i> 返回问题记录
                    </a>
                    <button class="print-button" class="btn btn-primary btn-sm">
                        <i class="fas fa-print mr-1"></i> 打印
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle mr-1"></i> 打印预览模式。点击"打印"按钮开始打印，或使用浏览器的打印功能（Ctrl+P）。
                </div>
            </div>
        </div>
    </div>

    <!-- 打印内容 -->
    <div class="print-preview">
        <!-- 第一页：问题记录列表 -->
        <div class="print-page avoid-break">
            <div class="print-page-indicator no-print">第1页</div>
            
            <!-- 页眉 -->
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 食堂问题记录</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>
            
            <!-- 问题记录列表 -->
            <div class="section-title">问题记录列表</div>
            {% if issues %}
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th width="5%">序号</th>
                        <th width="20%">问题类型</th>
                        <th width="15%">发现时间</th>
                        <th width="10%">优先级</th>
                        <th width="10%">状态</th>
                        <th width="40%">问题描述</th>
                    </tr>
                </thead>
                <tbody>
                    {% for issue in issues %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ issue.issue_type }}</td>
                        <td>{{ issue.found_time|safe_datetime('%H:%M') }}</td>
                        <td>
                            {% if issue.priority == 'high' %}
                            <span class="badge-high">高</span>
                            {% elif issue.priority == 'medium' %}
                            <span class="badge-medium">中</span>
                            {% else %}
                            <span class="badge-low">低</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if issue.status == 'pending' %}
                            <span class="badge-pending">待处理</span>
                            {% else %}
                            <span class="badge-resolved">已解决</span>
                            {% endif %}
                        </td>
                        <td>{{ issue.description|truncate(50) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <p class="text-muted">暂无问题记录</p>
            {% endif %}
        </div>
        
        <!-- 问题记录详情页 -->
        {% for issue in issues %}
        <div class="print-page avoid-break page-break">
            <div class="print-page-indicator no-print">问题记录 {{ loop.index }}</div>
            
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 问题记录详情</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>
            
            <div class="section-title">{{ issue.issue_type }}</div>
            
            <div class="row info-row">
                <div class="col-md-4">
                    <span class="info-label">问题类型：</span> {{ issue.issue_type }}
                </div>
                <div class="col-md-4">
                    <span class="info-label">发现时间：</span> {{ issue.found_time|safe_datetime('%Y-%m-%d %H:%M') }}
                </div>
                <div class="col-md-4">
                    <span class="info-label">优先级：</span> 
                    {% if issue.priority == 'high' %}
                    <span class="badge-high">高</span>
                    {% elif issue.priority == 'medium' %}
                    <span class="badge-medium">中</span>
                    {% else %}
                    <span class="badge-low">低</span>
                    {% endif %}
                </div>
            </div>
            
            <div class="row info-row">
                <div class="col-md-4">
                    <span class="info-label">状态：</span> 
                    {% if issue.status == 'pending' %}
                    <span class="badge-pending">待处理</span>
                    {% else %}
                    <span class="badge-resolved">已解决</span>
                    {% endif %}
                </div>
                <div class="col-md-4">
                    <span class="info-label">记录人：</span> {{ issue.reporter_name or '未知' }}
                </div>
                <div class="col-md-4">
                    <span class="info-label">记录时间：</span> {{ issue.created_at|safe_datetime('%Y-%m-%d %H:%M') }}
                </div>
            </div>
            
            <div class="section-title">问题描述</div>
            <div class="row info-row">
                <div class="col-md-12">
                    <p>{{ issue.description or '无' }}</p>
                </div>
            </div>
            
            <div class="section-title">解决方案</div>
            <div class="row info-row">
                <div class="col-md-12">
                    <p>{{ issue.solution or '无' }}</p>
                </div>
            </div>
            
            {% if issue.resolved_time %}
            <div class="section-title">解决信息</div>
            <div class="row info-row">
                <div class="col-md-6">
                    <span class="info-label">解决时间：</span> {{ issue.resolved_time|safe_datetime('%Y-%m-%d %H:%M') }}
                </div>
                <div class="col-md-6">
                    <span class="info-label">解决人：</span> {{ issue.resolver_name or '未知' }}
                </div>
            </div>
            {% endif %}
            
            {% if issue.photo_paths %}
            <div class="section-title">问题照片</div>
            <div class="row">
                {% for path in issue.photo_paths.split(';') %}
                <div class="col-md-4 mb-3">
                    <img src="{{ path }}" alt="问题照片" class="img-fluid img-thumbnail">
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        {% endfor %}
        
        <!-- 签名区域 -->
        <div class="print-page avoid-break page-break">
            <div class="print-page-indicator no-print">签名页</div>
            
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 食堂问题记录</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>
            
            <div class="section-title">签名确认</div>
            <div class="row mt-5">
                <div class="col-md-4">
                    <div class="text-center">
                        <p>记录人签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <p>食堂负责人签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <p>学校负责人签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-5">
                <p>（本文档由系统自动生成，打印后有效）</p>
            </div>
        </div>
    </div>
</div>

<!-- 打印控制按钮（固定在右下角） -->
<div class="print-controls no-print">
    <button class="print-button"><i class="fas fa-print mr-1"></i> 打印</button>
    <button data-action="safe-navigate" data-navigate-code="window.location.href=" style="cursor: pointer;"{{ url_for('daily_management.issues', log_id=log.id) }}'"><i class="fas fa-times mr-1"></i> 取消</button>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 打印预览初始化
        $('.print-page').each(function(index) {
            $(this).find('.print-page-indicator').text('第' + (index + 1) + '页');
        });
    });
</script>
{% endblock %}
